import os
import subprocess
import json
from datetime import datetime
from airflow.decorators import dag, task
from airflow.hooks.base import BaseHook

team_name = 'datalab'

def get_environment():
    conn = BaseHook.get_connection(f'{team_name}_aws_conn')
    print("extra: " + conn.get_extra())
    conn_extra = json.loads(conn.get_extra())
    print("extra loaded: " + str(conn_extra))
    env = os.environ.copy()
    env.update(dict(
        AWS_IAM_ROLE_ASSUME=conn_extra.get("role_arn"),
        AWS_REGION=conn_extra.get("region_name", "us-east-1")
    ))
    return env


@dag(
    dag_id='test_file_conversion',
    schedule_interval="@once",
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=['testing']
)
def test_file_conversion():
    
    @task()
    def convert_file():
        print('Start to convert file...')
        
        # File paths - update these for your actual files
        source_file = 's3://signal-hub-365007379098-us-east-1/archive/us/ems/source_linkage/ems_linkage.X316.20250908024228.rng02.gz'
        destination_file = 's3://signal-hub-365007379098-us-east-1/us/experian/datalab/c51336a/test-file.bz2'
        
        env = get_environment()

        
        # Command as requested: download -> unzip -> bzip2 -> upload
        # Use pipefail to ensure pipeline fails if any command fails
        cmd = f'set -o pipefail && aws s3 cp {source_file} - | zcat | lbzip2 -c | aws s3 cp - {destination_file}'
        
        print(f'Executing: {cmd}')
        print(f'Source: {source_file}')
        print(f'Destination: {destination_file}')
        
        # Execute with subprocess.Popen
        process = subprocess.Popen(
            cmd,
            shell=True,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Get output
        stdout, stderr = process.communicate()
        
        # Print results
        print(f'Return code: {process.returncode}')
        print(f'STDOUT: {stdout.decode("utf-8") if stdout else "No output"}')
        print(f'STDERR: {stderr.decode("utf-8") if stderr else "No errors"}')
        
        # Check for errors
        if process.returncode != 0:
            raise Exception(f'Command failed with return code {process.returncode}: {stderr.decode("utf-8")}')
        
        if stderr and any(x in stderr.decode("utf-8").lower() for x in ['error', 'failed']):
            raise Exception(f'Command completed but with errors: {stderr.decode("utf-8")}')
        
        print('File conversion completed successfully!')
        return {
            'source': source_file,
            'destination': destination_file,
            'return_code': process.returncode
        }
    
    convert_file()


# Create the DAG instance
test_dag = test_file_conversion()
