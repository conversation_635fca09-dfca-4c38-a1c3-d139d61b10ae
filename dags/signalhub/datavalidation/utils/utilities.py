import boto3
import json
import logging
import io
import os
import gzip
import bz2
import zipfile
import hashlib
import zlib
import requests
import sqlite3
import psycopg2
import socket
import yaml
import subprocess
import shutil
import traceback
from datetime import datetime
from textwrap import dedent


def format_timestamp(ts_string, ts_format='%Y%m%d%H%M%S'):
    try:
        return datetime.strptime(ts_string, ts_format).replace(microsecond=0)
    except Exception:
        return None


def normalize_timestamp_str(file_name, ts_str):
    if len(ts_str) < 8:
        return None
    # e.g. ems_cell.X231.20180821.rng00.gz.20180822082237
    if file_name.startswith(('ems')):
        return ts_str[:8].ljust(14, '0')
    # e.g. truvue_cons_related_name.190222000000.190222000000.01.pkzip.20190222151938
    elif file_name.startswith('truvue'):
        return ('20' + ts_str[:6]).ljust(14, '0')
    return ts_str[-14:][:8].ljust(14, '0')


def get_date_from_cc1_file(file_name):
    if 'manifest' in file_name:
        return file_name.split('.')[1]
    else:
        return file_name.split('.')[2][:8]


def get_last_modified_date(file_obj, format='%Y%m%d'):
    return file_obj['LastModified'].strftime(format)


def get_receiving_date_from_filename(full_path):
    '''
    should not be applied to CC2
    '''
    file_name = os.path.basename(full_path)
    if 'manifest' in file_name and any(x in full_path for x in ['fraudnet', 'crosscore_v1', 'cis/cem']):
        return file_name.split('.')[1][:8]
    else:
        return file_name.split('.')[2][:8]


def get_receiving_date(full_path, file_obj):

    if 'crosscore_v2' in full_path:
        return get_last_modified_date(file_obj)
    elif 'PID' in full_path:
        return os.path.basename(full_path).split('.')[1].replace('-', '')
    else:
        try:
            return get_receiving_date_from_filename(full_path)
        except:
            return None


def get_unzip_cmd_from_file_name(file, format='csv'):
    if any(x in file for x in ['.gz', '.pkzip']):
        return 'zcat'
    elif '.bz2' in file:
        return 'bzcat'
    elif format == 'csv':
        return 'cat'
    else:
        return None


def get_bucket_name_from_filename(filename):
    return filename.replace('s3://', '').split('/')[0]


def get_bucket_and_key_from_filename(filename):
    '''the input filename is the full path starting with s3://'''
    bucket = filename.replace('s3://', '').split('/')[0]
    key = filename.replace(f's3://{bucket}/', '')
    return bucket, key


def get_head_object(boto3_client, bucket, key, encrypt=False, args=None):
    tmp_kwargs = {'Bucket': bucket, 'Key': key}
    return boto3_client.head_object(**(tmp_kwargs if not encrypt else {**tmp_kwargs, **args}))


def get_head_object_using_airflow(bucket, key, conn_id, encrypt=False, args=None):
    from airflow.hooks.S3_hook import S3Hook
    tmp_kwargs = {'Bucket': bucket, 'Key': key}
    return S3Hook(conn_id).get_conn().head_object(**(tmp_kwargs if not encrypt else {**tmp_kwargs, **args}))


def copy_file_on_s3(source_bucket, source_key, dest_bucket, dest_key, encrypt=False, args=None):
    '''copy file between two S3 locations using boto3'''

    s3 = boto3.resource('s3')
    copy_source = {
        'Bucket': source_bucket,
        'Key': source_key}
    s3.meta.client.copy(copy_source, dest_bucket, dest_key)
    #return get_head_object(boto3_client, dest_bucket, dest_key, encrypt=encrypt, args=args)


def copy_file_on_s3_using_airflow_old(source_bucket, source_key, dest_bucket, dest_key, conn_id):
    '''copy file between two S3 locations using Airflow hook'''
    from airflow.hooks.S3_hook import S3Hook
    hook = S3Hook(conn_id)
    hook.copy_object(source_key, dest_key, source_bucket_name=source_bucket, dest_bucket_name=dest_bucket)


def copy_file_on_s3_using_airflow(source_bucket, source_key, dest_bucket, dest_key, conn_id=None):
    '''copy file between two S3 locations using Airflow hook'''
    from airflow.providers.amazon.aws.hooks.base_aws import AwsBaseHook
    session = AwsBaseHook(conn_id, client_type='s3').get_session()
    try:
        s3 = session.resource('s3')
        copy_source = {
            'Bucket': source_bucket,
            'Key': source_key}
        s3.meta.client.copy(copy_source, dest_bucket, dest_key)
    except:
        logging.warning(f'Failed to copy file {source_bucket}/{source_key}')        


def check_file_corruption_using_airflow(file, format='csv'):

    if any(x in file for x in ['.gz', '.pkzip']):
        unzip_cmd = 'zcat'
    elif '.bz2' in file:
        unzip_cmd = 'bzcat'
    elif format == 'csv':
        unzip_cmd = 'cat'
    else:
        logging.warning(f'{file} is not csv file  or has unrecognized compression type, skipping...')
        return

    cmd = f'aws s3 cp {file} - | {unzip_cmd} | wc -l'
    env = creds if on_airflow else None
    process_line_count = subprocess.Popen(cmd, env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    std_out, std_err = process_line_count.communicate()
    if any(x in str(std_err) for x in ['error', 'Error', 'ERROR']):
        logging.warning(f'File is probably corrupted')
        print('STD_ERR:', std_err)
        return False
    else:
        return True 


def decrypt_gpg_file(boto3_client, file, gpg_key, gpg_passphrase, always_trust=False, local_path='./tmp'):

    import gnupg

    if not gpg_passphrase:
        logging.warning('gpg passphrase not provided, skipping...')
        return

    bucket, key = get_bucket_and_key_from_filename(file)

    gpg = gnupg.GPG()
    private_key = gpg.import_keys(gpg_key)

    try:
        encrypted_content = boto3_client.get_object(Bucket=bucket, Key=key)['Body'].read()
        decrypted_content = gpg.decrypt_file(io.BytesIO(encrypted_content), passphrase=gpg_passphrase, always_trust=always_trust)
        if decrypted_content.ok:
            boto3_client.put_object(Bucket=bucket, Key=key.replace('.gpg', ''), Body=decrypted_content.data)
            logging.info(f'Successfully decrypted file {file}')
            return 's3://' + bucket + '/' + key.replace('.gpg', '')
        else:
            logging.warning(f'Failed to decrypt file {file}: {decrypted_content.status}; {decrypted_content.stderr}')
            return None
    except:
        logging.warning(f'Failed to decrypt file {file}')
        return None


def generate_subprocess_command(cmd, mem_dir):
    bash_location = f"{mem_dir}/command.sh"
    with open(bash_location, 'w') as f:
        f.write(dedent(f"""\
            #!/bin/bash
            {cmd}
        """))
    return ["bash", bash_location]


def decrypt_gpg_file_using_aws_cli(file_name, gpg_private_key, gpg_passphrase, env, mem_dir):

    decrypted_file = file_name.replace('.gpg', '').replace('.pgp', '')

    try:
        gpg_key_location = f"{mem_dir}/gpg_key"
        with open(gpg_key_location, 'w') as f:
            f.write(gpg_private_key)

        decrypt_cmd = f'gpg --batch --import {gpg_key_location}\n'
        decrypt_cmd += f'aws s3 cp {file_name} - | gpg --ignore-mdc-error --pinentry-mode=loopback -d --batch --yes --always-trust --passphrase "{gpg_passphrase}" | aws s3 cp - {decrypted_file}'
        cmd = generate_subprocess_command(decrypt_cmd, mem_dir) 
        process_decrypt = subprocess.Popen(cmd, env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        std_out, std_err = process_decrypt.communicate()
        print('STD_ERR:', std_err)
        print('STD_OUT:', std_out)
        if any(x in str(std_err) for x in ['error', 'Error', 'ERROR']):
            logging.warning(f'Failed to decrypt file {file_name}')
            return None
        else:
            logging.info(f'Successfully decrypted file {file_name}')
            return decrypted_file
    except:
        logging.warning(f'Failed to decrypt file {file_name}')
        return None


def unzip_file_using_aws_cli(boto3_client, source, creds, mem_dir):
    '''
    source is the original zipped file on S3.
    Compress the unzipped content into bz2.
    The unzipped file is uploaded to the same S3 directory as the original file
    '''
    try:
        file_name = os.path.basename(source)
        local_path = '{}/{}'.format(mem_dir, file_name)
        unzipped_local_path = local_path.replace('.zip', '.bz2')
        download_file_from_s3_to_local(boto3_client, source, local_path)

        unzip_cmd = f"""unzip -p {local_path} | bzip2 -c > {unzipped_local_path}"""
        unzip_cmd = generate_subprocess_command(unzip_cmd, mem_dir)
        process_unzip = subprocess.Popen(unzip_cmd, env=creds, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        std_out, std_err = process_unzip.communicate()
        print('STD_ERR:', std_err)

        unzipped_file = source.replace(".zip", ".bz2")
        upload_file_from_local_to_s3(boto3_client, unzipped_local_path, unzipped_file)

        # clean up files in local dir
        os.remove(local_path)
        os.remove(unzipped_local_path)

        return unzipped_file
    except:
        logging.warning(f'Failed to unzip filee {source}')
        return None


def calculate_checksum(boto3_client, file, dry_run=False):

    if dry_run or file is None:
        return None

    bucket, key = get_bucket_and_key_from_filename(file)
    file_obj = boto3_client.get_object(Bucket=bucket, Key=key)
    body = file_obj["Body"].read()

    ## calculate checksum on the original file before decryption or conversion
    try:
        if os.path.basename(file).startswith('cem'): # CRC32 checksum
            checksum = hex(zlib.crc32(body) & 0xffffffff)[2:].upper()
        else: # md5 checksum
            checksum = hashlib.md5(body).hexdigest().upper()
    except:
        logging.warning(f'Failed to calculate checksum for file {file}...')
        checksum = None

    return checksum


def calculate_line_count(file, on_airflow=True, creds=None):

    if any(x in file for x in ['.gz', '.pkzip']):
        unzip_cmd = 'zcat'
    elif '.bz2' in file:
        unzip_cmd = 'bzcat'
    else:
        logging.warning(f'{file} has unrecognized compression type, unable to calculate line count for it...')
        return

    try:
        cmd = f'aws s3 cp {file} - | {unzip_cmd} | wc -l'
        env = creds if on_airflow else None
        process_line_count = subprocess.Popen(cmd, env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
        std_out, std_err = process_line_count.communicate()
        if any(x in str(std_err) for x in ['error', 'Error', 'ERROR']):
            logging.warning(f'Failed to calculate line count for file {file}')
            print('STD_ERR:', std_err)
            return None
        else:
            line_count = std_out.decode('utf-8').strip()
            #logging.info(f'Successfully calculate line count for file {file_name}')
            return line_count
    except:
        logging.warning(f'Failed to calculate line count for file {file}')
        return None


def calculate_checksum_using_aws_cli(file, creds=None, mem_dir=None):

    try:
        checksum_cmd = 'cksum' if os.path.basename(file).startswith('cem') else 'md5sum'
        cmd = f'aws s3 cp {file} - | {checksum_cmd}'
        cmd = generate_subprocess_command(cmd, mem_dir)
        process_checksum = subprocess.Popen(cmd, env=creds, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        std_out, std_err = process_checksum.communicate()
        if any(x in str(std_err) for x in ['error', 'Error', 'ERROR']):
            logging.warning(f'Failed to calculate checksum for file {file}')
            print('STD_ERR:', std_err)
            return None
        else:
            raw_checksum = std_out.decode('utf-8').strip().split(' ')[0].strip()
            if os.path.basename(file).startswith('cem'):
                checksum = hex(int(raw_checksum) & 0xffffffff)[2:].upper()
            else:
                checksum = std_out.decode('utf-8').strip().split(' ')[0].strip().upper()
            return checksum
    except:
        logging.warning(f'Failed to calculate checksum for file {file}')
        return None


def calculate_line_count_using_aws_cli(file, in_local=False, creds=None, mem_dir=None, format='csv'):

    unzip_cmd = get_unzip_cmd_from_file_name(file, format=format)
    if unzip_cmd is None:
        logging.warning(f'{file} has unrecognized compression type, unable to calculate line count for it...')
        return

    try:
        if in_local:
            local_file = download_file_from_s3_to_local_using_aws_cli(file, creds=creds, mem_dir=mem_dir)
            cmd = f'{unzip_cmd} {local_file} | wc -l'
            process_line_count = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
        else:
            cmd = f'aws s3 cp {file} - | {unzip_cmd} | wc -l'
            cmd = generate_subprocess_command(cmd, mem_dir)
            process_line_count = subprocess.Popen(cmd, env=creds, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        std_out, std_err = process_line_count.communicate()
        if any(x in str(std_err) for x in ['error', 'Error', 'ERROR']):
            logging.warning(f'Failed to calculate line count for file {file}')
            print('STD_ERR:', std_err)
            return None
        else:
            line_count = std_out.decode('utf-8').strip()
            #logging.info(f'Successfully calculate line count for file {file_name}')
            return line_count
    except:
        logging.warning(f'Failed to calculate line count for file {file}')
        return None


def calculate_checksum_and_line_count_using_aws_cli(boto3_client, file, preprocessed_file=None, creds=None, mem_dir=None):
    '''
    file represents the original file from which we calculate checksum
    preprocessed_file represents decrypted or unzipped file from which we calculate line count
    '''

    ## checksum is calculated only for the original file
    checksum = calculate_checksum_using_aws_cli(file, creds=creds, mem_dir=mem_dir)

    if preprocessed_file is not None and preprocessed_file != file:
        ## check if the preprocessed file exist
        try:
            bucket, key = get_bucket_and_key_from_filename(preprocessed_file)
            head = boto3_client.head_object(Bucket=bucket, Key=key)
        except:
            logging.warning(f'Preprocessed file {preprocessed_file} does not exist, skipping line count...')
            return checksum, None

    file_for_line_count = preprocessed_file if preprocessed_file is not None else file

    ## For some large data files, skip line count for now
    dataset_to_skip_line_count = ['nvdb', 'ems_email']
    if any(x in file_for_line_count for x in dataset_to_skip_line_count):
        line_count = None
    else:
        line_count = calculate_line_count_using_aws_cli(file_for_line_count, in_local=False, creds=creds, mem_dir=mem_dir)

    return checksum, line_count


def delete_file_in_local(file_name):

    cmd = f'rm {file_name}'
    process_delete = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    std_out, std_err = process_delete.communicate()
    print('STD_OUT:', std_out)
    if any(x in str(std_err) for x in ['error', 'Error', 'ERROR']):
        logging.warning(f'Failed to delete {file_name} in local storage')
        print('STD_ERR:', std_err)


def convert_file_to_bz2_using_aws_cli(file_name, convert_in_local=False, creds=None, mem_dir=None, format='csv', calculate_converted_file_checksum=True):

    if '.zip' in file_name:
        logging.warning('File is zipped, please unzip it first.')
        return

    if '.bz2' in file_name:
        logging.warning('File is already bz2 compressed.')
        return

    unzip_cmd = get_unzip_cmd_from_file_name(file_name, format=format)
    if unzip_cmd is None:
        logging.warning(f'{file} has unrecognized compression type, unable to calculate line count for it...')
        return

    if any(x in file_name for x in ['.csv', '.gz', '.pkzip']):
        converted_file = file_name.replace('.csv', '.bz2').replace('.gz', '.bz2').replace('.pkzip', '.bz2')
    elif format == 'csv':
        converted_file = file_name + '.bz2'
        
    if convert_in_local:
        local_file = download_file_from_s3_to_local_using_aws_cli(file_name, creds=creds, mem_dir=mem_dir)
        print(f'File has been downloaded to local: {local_file}')
        if any(x in local_file for x in ['.csv', '.gz', '.pkzip']):
            local_converted_file = local_file.replace('.csv', '.bz2').replace('.gz', '.bz2').replace('.pkzip', '.bz2')
        elif format == 'csv':
            local_converted_file = local_file + '.bz2'
        cmd = f'{unzip_cmd} {local_file} | lbzip2 -c > {local_converted_file}'
        #cmd = f'ls /tmp/'
        process_convert = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
        print('File has been converted to bz2')
        upload_file_from_local_to_s3_using_aws_cli(local_converted_file, converted_file, creds=creds, mem_dir=mem_dir)
        print(f'File has been uploaded to s3: {converted_file}')
        delete_file_in_local(converted_file)
        delete_file_in_local(local_converted_file)
    else:
        cmd = f'aws s3 cp {file_name} - | {unzip_cmd} | lbzip2 -c | aws s3 cp - {converted_file}'
        cmd = generate_subprocess_command(cmd, mem_dir)
        #env = creds if on_airflow else None
        process_convert = subprocess.Popen(cmd, env=creds, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    std_out, std_err = process_convert.communicate()
    print('STD_OUT:', std_out)
    if any(x in str(std_err) for x in ['error', 'Error', 'ERROR']):
        logging.warning(f'Failed to convert {file_name} to bz2')
        print('STD_ERR:', std_err)
        return None, None

    converted_file_checksum = None
    if calculate_converted_file_checksum:
        converted_file_checksum = calculate_checksum_using_aws_cli(converted_file, creds=creds, mem_dir=mem_dir)

    return converted_file, converted_file_checksum


def calculate_checksum(boto3_client, file, dry_run=False):

    if dry_run or file is None:
        return None

    bucket, key = get_bucket_and_key_from_filename(file)
    file_obj = boto3_client.get_object(Bucket=bucket, Key=key)
    body = file_obj["Body"].read()

    ## calculate checksum on the original file before decryption or conversion
    try:
        if os.path.basename(file).startswith('cem'): # CRC32 checksum
            checksum = hex(zlib.crc32(body) & 0xffffffff)[2:].upper()
        else: # md5 checksum
            checksum = hashlib.md5(body).hexdigest().upper()
    except:
        logging.warning(f'Failed to calculate checksum for file {file}...')
        checksum = None

    return checksum


def calculate_checksum_and_line_count(boto3_client, file, dry_run=False):
    '''
    This function operates on a data file:
        + calculate checksum and line count
        + if checksum and line count calculation fails, label the file as corrupted
    '''

    if dry_run or file is None:
        return None, None

    bucket, key = get_bucket_and_key_from_filename(file)
    file_obj = boto3_client.get_object(Bucket=bucket, Key=key)
    body = file_obj["Body"].read()

    ## calculate checksum on the original file before decryption or conversion
    try:
        if os.path.basename(file).startswith('cem'): # CRC32 checksum
            checksum = hex(zlib.crc32(body) & 0xffffffff)[2:].upper()
        else: # md5 checksum
            checksum = hashlib.md5(body).hexdigest().upper()
            print(checksum)
    except:
        logging.warning(f'Failed to calculate checksum for file {file}...')
        checksum = None

    if '.gpg' in file:
        ## check if the decrypted file exist
        try:
            head = boto3_client.head_object(Bucket=bucket, Key=key.replace('.gpg', ''))
        except:
            logging.warning('Decrypted file {} does not exist, skipping line count...'.format(file.replace('.gpg', '')))
            return checksum, None
        decrypted_body = boto3_client.get_object(Bucket=bucket, Key=key.replace('.gpg', ''))["Body"].read()

    try:
        # calculate line count
        body_for_line_cnt = decrypted_body if '.gpg' in file else body
        with io.BytesIO(body_for_line_cnt) as tf:
            if '.gz' in key:
                print('extract from gzip')
                with gzip.open(tf, 'rb') as zipf:
                    file_content = zipf.read()
            elif '.zip' in key:
                print('extract from zip')
                with zipfile.ZipFile(tf, 'r') as zipf:
                    raw_file_name = zipf.namelist()[0]
                    print(raw_file_name)
                    with zipf.open(raw_file_name) as file:
                        file_content = file.read()
            else:
                print('extract from bz2')
                with bz2.BZ2File(tf, 'r') as zipf:
                    file_content = zipf.read()
            lines = list(filter(None, file_content.decode('utf-8').split('\n')))
            line_count = len(lines)
    except:
        print('The file is possibly corrupted')
        checksum = 'corrupted'
        line_count = 0

    return checksum, line_count


def convert_file_to_bz2(boto3_client, full_file_name, calculate_converted_file_checksum=True):
    '''
    need to check corrupted file
    '''
    try:
        # Get the compression type depending on the file
        # Read gzip data
        bucket, key = get_bucket_and_key_from_filename(full_file_name)
        file_obj = boto3_client.get_object(Bucket=bucket, Key=key)
        body = file_obj["Body"].read()
        # convert to bz2 and upload back to landing zone
        #file_type = '.gz' if '.gz' in full_file_name else '.pkzip'
        if '.gz' in full_file_name:
            file_type = '.gz'
        elif '.pkzip' in full_file_name:
            file_type = '.pkzip'
        if '.zip' in full_file_name:
            file_type = '.zip'
        converted_file = full_file_name.replace(file_type, '.bz2')
        converted_key = key.replace(file_type, '.bz2')
        with io.BytesIO(body) as tf:
            if file_type != '.zip':
                with gzip.open(tf, 'rb') as zipf:
                    file_content = zipf.read()
            else:
                with zipfile.ZipFile(tf, 'r') as zipf:
                    raw_file_name = zipf.namelist()[0]
                    print('raw_file_name:', raw_file_name)
                    with zipf.open(raw_file_name) as file:
                        file_content = file.read()
            bz2_content = bz2.compress(file_content)
            boto3_client.put_object(Bucket=bucket, Key=converted_key, Body=bz2_content)

        checksum = None
        if calculate_converted_file_checksum:
            if os.path.basename(full_file_name).startswith('cem'): # CRC32 checksum
                checksum = zlib.crc32(bz2_content)
            else: # md5 checksum
                checksum = hashlib.md5(bz2_content).hexdigest().upper() 

        logging.info(f"Successfully converted {full_file_name} to {converted_key}")

        return converted_file, checksum

    except Exception as e:
        logging.error('Error occurred while converting %s to .bz2, will pass on to next file, %s'
                      % (full_file_name, traceback.format_exc()))
        print(traceback.format_exc())
        return None, None


def download_file_from_s3_to_local(boto3_client, s3_path, local_path):
    bucket, key = get_bucket_and_key_from_filename(s3_path)
    boto3_client.download_file(bucket, key, local_path)


def download_file_from_s3_to_local_using_aws_cli(file_name, on_airflow=True, creds=None, mem_dir=None):

    bucket, key = get_bucket_and_key_from_filename(file_name)
    cmd = f'aws s3 cp {file_name} {mem_dir}/{os.path.basename(key)}'
    if on_airflow:
        cmd = generate_subprocess_command(cmd, mem_dir)
    env = creds if on_airflow else None
    process = subprocess.Popen(cmd, env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    std_out, std_err = process.communicate()
    if any(x in str(std_err) for x in ['error', 'Error', 'ERROR']):
        logging.warning(f'Failed to download {file_name} to local')
        print('STD_ERR:', std_err)
        return None

    return f'{mem_dir}/{os.path.basename(key)}'


def upload_file_from_local_to_s3(boto3_client, local_path, s3_path):
    bucket, key = get_bucket_and_key_from_filename(s3_path)
    print(bucket, key)
    boto3_client.upload_file(local_path, bucket, key)


def upload_file_from_local_to_s3_using_aws_cli(local_file, s3_path, on_airflow=True, creds=None, mem_dir=None):

    cmd = f'aws s3 cp {local_file} {s3_path}'
    if on_airflow:
        cmd = generate_subprocess_command(cmd, mem_dir)
    env = creds if on_airflow else None
    process = subprocess.Popen(cmd, env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    std_out, std_err = process.communicate()
    if any(x in str(std_err) for x in ['error', 'Error', 'ERROR']):
        logging.warning(f'Failed to upload {local_file} to S3')
        print('STD_ERR:', std_err)


def download_and_parse_config_file(boto3_client, s3_path, local_config_file_dir, format='yaml'):
    config_file_path_local = f'{local_config_file_dir}/' + s3_path.split('/')[-1]
    download_file_from_s3_to_local(boto3_client, s3_path, config_file_path_local)
    if format == 'yaml':
        return parse_yaml_config_file(config_file_path_local)


def parse_config_file_from_s3(s3_hook, s3_path):
    '''for now, only handle yaml file using s3 hook, and only works for Airflow'''
    bucket, key = get_bucket_and_key_from_filename(s3_path)
    content = s3_hook.read_key(key, bucket_name=bucket)
    return yaml.safe_load(content)


def create_local_directory(dir_path):
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)
        print("Directory " , dir_path ,  " Created ")
    else:    
        print("Directory " , dir_path ,  " already exists") 


def create_sqlite_connection(database_file, timeout=60):
    logging.info(f'Creating database connection to {database_file}...')
    try:
        conn = sqlite3.connect(database_file, timeout=timeout, detect_types=sqlite3.PARSE_DECLTYPES)
        conn.isolation_level = None
        return conn
    except sqlite3.Error as e:
        logging.error(f'DB connection failed: {e}, exiting...')
        exit(1)


def create_connection(database_hostname, username, password, database='postgres'):
    logging.info(f'Creating database connection to {database_hostname}...')
    try:
        #keepalive_kwargs = {
        #    "keepalives": 1,
        #    "keepalives_idle": 30,
        #    "keepalives_interval": 5,
        #    "keepalives_count": 5,
        #}
        conn = psycopg2.connect(host=database_hostname, user=username, password=password, dbname=database)
        conn.autocommit = True
        return conn
    except psycopg2.Error as e:
        logging.error(f'DB connection failed: {e}, exiting...')
        exit(1)


def db_operation(conn, sql, values):
    data = list()
    try:
        c = conn.cursor()
        c.execute(sql, values)
        data = c.fetchall() if c.description is not None else None
    except psycopg2.Error as e:
        logging.error(f'DB operation failed while executing: {sql}, Database ERROR: {e}')
    except Exception as e:
        logging.error(f'DB operation failed while executing: {sql}, ERROR: {e}')
    return data


def create_boto3_client(config):
    session = boto3.session.Session()
    my_client = session.client(
        service_name='s3',
        aws_access_key_id=config['archive']['accessKey'],
        #use_ssl=True,
        aws_secret_access_key=config['archive']['secretKey'],
        #verify=config['CAs']
    )
    return my_client


def create_boto3_client_on_airflow(config):
    try:
        session = boto3.session.Session()
        my_client = session.client(
            service_name='s3',
            region_name=config['region_name'],
            aws_access_key_id=config['accessKey'],
            aws_secret_access_key=config['secretKey'],
            #use_ssl=True,
            #verify=config['CAs']
        )
        return my_client
    except:
        print('Creation of s3 client failed')


def check_runtime_environment(boto3_client, local_config_file_dir, run_time_config):
    config_file = run_time_config['storage_config_file']
    encryption_key_file = run_time_config['encryption_key_file']
    database_file = run_time_config['database']
    logging.info('Checking runtime environment...')

    print(f'Checking encryption key file {encryption_key_file} for: non empty key...')
    download_file_from_s3_to_local(boto3_client, run_time_config['encryption_key_file'], f'{local_config_file_dir}/encryption_key')
    with open(f'{local_config_file_dir}/encryption_key') as f:
        encryption_key = f.readline().strip()
    if not encryption_key:
        logging.error(f'Encryption key not found in {encryption_key_file}, exiting...')
        exit(1)
    logging.info('Successfully validated encryption key file.')

    #logging.info(f'Checking database file {database_file} for: existence, writable...')
    #if not os.access(database_file, os.W_OK):
    #    logging.error('Database file does not exist or is not writable, exiting...')
    #    exit(1)
    #logging.info('Successfully validated database file.')

    logging.info(f'Checking config file {config_file} for: existence, readable, content...')
    #if not os.access(config_file, os.R_OK):
    #    logging.error('Config file does not exist or is not readable, exiting...')
    #    exit(1)
    #else:
    #with open(config_file) as f:
    #    config = json.load(f)
    #if not (config['archive']['accessKey'] and config['archive']['secretKey']
    #        and config['CAs'] and config['archive']['url']):
    #    logging.error('Config file missing information, exiting...')
    #    exit(1)
    #logging.info('Successfully validated config file.')

    #logging.info(f'Checking CAs {config["CAs"]} for: existence, readable...')
    #if not os.access(config['CAs'], os.R_OK):
    #    logging.error('CAs do not exist or are not readable, exiting...')
    #    exit(1)

    #with open(config['CAs']) as f:
    #    f_content = f.read()
    #    if not (f_content and f_content.strip()):
    #        logging.error('CAs content empty, exiting...')
    #        exit(1)
    #    logging.info('Successfully validated CAs.')

    logging.info('Successfully validated runtime environment.')
    return encryption_key


def ensure_single_instance(signature):
    try:
        global abstract_socket
        abstract_socket = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
        # Create an abstract socket, by prefixing it with null.
        abstract_socket.bind(f'\0{signature}')
    except socket.error as e:
        error_code = e.args[0]
        error_string = e.args[1]
        print(f'Process (SIGNATURE={signature}) already running ({error_code}: {error_string}). Exiting')
        exit(0)


def config_logging_level(app_logging):
    app_logging.getLogger('boto3').setLevel(app_logging.CRITICAL)
    app_logging.getLogger('botocore').setLevel(app_logging.CRITICAL)
    app_logging.getLogger('s3transfer').setLevel(app_logging.CRITICAL)
    app_logging.getLogger('urllib3').setLevel(app_logging.CRITICAL)


def parse_yaml_config_file(yaml_config_file):
    try:
        config_dict = yaml.safe_load(open(yaml_config_file))
    except Exception as e:
        print(f'Unable to read config file {yaml_config_file}, exiting...')
        exit(1)
    return config_dict


def remove_file(boto3_client, full_path, dry_run=False):
    try:
        if not dry_run:
            bucket, key = get_bucket_and_key_from_filename(full_path)
            boto3_client.delete_object(Bucket=bucket, Key=key)
    except Exception as e:
        logging.warning(f'Failed to remove file {full_path}: {e}')
        return
    logging.info(f'File successfully removed{" (dry_run)" if dry_run else ""}.')
    return datetime.now().replace(microsecond=0)


def send_notification(msg, ms_teams_channel_url, https_proxy_url='http://10.248.147.6:9090', http_proxy_url='http://10.248.147.6:9090'):
    proxies = {
        'https': https_proxy_url,
        'http': http_proxy_url
    }
    headers = {'Content-Type': 'application/json'}
    payload = {'text': msg}
    r = requests.post(ms_teams_channel_url, data=json.dumps(payload), headers=headers, proxies=proxies)
    if r.status_code == requests.codes.ok:
        logging.info(f'Notification successfully sent, response: {r}')
    else:
        logging.error(f'Failed to send notification, response: {r}')
    return r.status_code


def move_file_on_s3_using_airflow(boto3_client, source_file_path, destination_folder, conn_id):
    source_bucket, source_key = get_bucket_and_key_from_filename(source_file_path)
    dest_bucket, dest_key = get_bucket_and_key_from_filename(destination_folder)
    dest_key = dest_key + os.path.basename(source_file_path)
    copy_file_on_s3_using_airflow(source_bucket, source_key, dest_bucket, dest_key, conn_id)
    remove_file(boto3_client, source_file_path)


def extract_metadata_from_file_name(full_path):

    PID_file_prefix = ['FIS_PID_TRAN', 'PID_KIQ', 'CSTL']

    file_name = os.path.basename(full_path)
    result = {'snapshot': None, 'snapshot_dt': None, 'part': None, 'invalid_name': 0}
    name_fields = file_name.split('.')

    # Handle snapshot and snapshot_dt
    if 'rentbureau' in file_name:
        snapshot = name_fields[1][:14]
    else:
        snapshot = name_fields[1][-14:]
    if any(file_name.startswith(x) for x in PID_file_prefix):
        snapshot = snapshot.replace('-', '').ljust(14, '0')
    snapshot_reformatted = format_timestamp(normalize_timestamp_str(file_name, snapshot))
    if snapshot_reformatted or file_name.startswith('ems'):
        result['snapshot'] = snapshot
    else:
        result['invalid_name'] = 1

    if any(file_name.startswith(x) for x in PID_file_prefix):
        result['snapshot_dt'] = snapshot_reformatted.date()
        return result

    # Handle creation time
    creation_time = format_timestamp(normalize_timestamp_str(file_name, name_fields[2][:14]))
    if not creation_time:
        result['invalid_name'] = 1
        if snapshot_reformatted or file_name.startswith('ems'):
            result['snapshot_dt'] = snapshot_reformatted.date()
    else:
        result['snapshot_dt'] = creation_time.date()

    if 'manifest' in file_name:
        return result

    compression_types = ('bz2', 'gz', 'pkzip', 'zip')

    part = name_fields[3]
    has_part = part not in compression_types

    if has_part:
        result['part'] = part
        type_ = name_fields[4] if not file_name.startswith('cc1') else name_fields[6]
        if type_ not in compression_types:
            result['invalid_name'] = 1

    return result


def extract_metadata_base(file, file_obj):
    # extract base metatdata from file (non file name related)
    result = dict()
    result['location'] = file
    file_name = os.path.basename(file)
    xfer_time_tmp = format_timestamp(file_name.rsplit('.', 1)[1])
    result['xfer_time'] = xfer_time_tmp
    if xfer_time_tmp:
        result['data_key'] = file_name.rsplit('.', 1)[0]
    else:
        # This file name does not contain received timestamp
        result['data_key'] = file_name

    result['last_modified_time'] = file_obj['LastModified'].strftime("%Y-%m-%d %H:%M:%S") if file_obj else None
    #result['last_modified_time'] = datetime.fromtimestamp(os.path.getmtime(file)).replace(microsecond=0)
    result['data_source'] = os.path.basename(os.path.dirname(file))
    result['data_set'] = result['data_source']
    # Specially handle gvap, truvue and emailinsight
    for x in ['gvap', 'cem', 'creditheader', 'truvue', 'emailinsight', 'fraudnet', 'crosscore_v1', 'PID']:
        if x in result['data_source']:
            result['data_set'] = file_name.split('.', 1)[0]
            break
    result['business_unit'] = os.path.basename(os.path.dirname(os.path.dirname(file)))
    result['file_size'] = file_obj['Size'] if file_obj else None
    result['invalid_name'] = 0
    return result
