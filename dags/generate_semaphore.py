import io
import os
import json
import yaml
import shutil
import subprocess
import boto3
from datetime import date, datetime, timedelta
from airflow import DAG
from airflow.operators.python import get_current_context
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import Python<PERSON><PERSON>ator
from airflow.operators.bash_operator import <PERSON><PERSON><PERSON><PERSON><PERSON>
from airflow.sensors.base_sensor_operator import BaseSensorOperator
from airflow.providers.amazon.aws.hooks.base_aws import AwsBaseHook
from airflow.providers.amazon.aws.hooks.secrets_manager import SecretsManagerHook
from airflow.hooks.base import BaseHook
from airflow.hooks.S3_hook import S3Hook
from airflow.models import Variable
from airflow.decorators import dag, task

from signalhub.datavalidation.utils.utilities import create_connection, db_operation, upload_file_from_local_to_s3_using_aws_cli
# from signalhub.datavalidation.utils.cleanup import is_snapshot_valid


team_name = 'datalab'
mem_dir = "/tmp"

def connect_to_postgres_db(**context):
    connection = BaseHook.get_connection(f'{team_name}_postgres_database_connection')
    db_conn = create_connection(connection.host, connection.login, connection.password)
    return db_conn


def get_environment():
    conn = BaseHook.get_connection(f'{team_name}_aws_conn')
    print("extra: " + conn.get_extra())
    conn_extra = json.loads(conn.get_extra())
    print("extra loaded: " + str(conn_extra))
    env = os.environ.copy()
    env.update(dict(
        AWS_IAM_ROLE_ASSUME=conn_extra.get("role_arn"),
        AWS_REGION=conn_extra.get("region_name", "us-east-1")
    ))
    return env


def get_list_of_valid_snapshots(conn, datasource, dataset, requested_snapshot_dt, incremental=False):

    ## Collect valid snapshot in the same month of the given snapshot_date
    ## Time filter should be based on snapshot but also need to use snapshot_dt (mainly for EMS files)
    requested_year = str(requested_snapshot_dt.year)
    requested_month = str(requested_snapshot_dt.month).zfill(2)

    if incremental:
        valid_snapshots = db_operation(conn, 
                    f'SELECT '
                    f'snapshot_dt, MAX(snapshot) as snapshot '
                    f'FROM data '
                    f'WHERE '
                    f'data_source = %s AND '
                    f'data_set = %s AND '
                    f'checksum_validated = 1 AND '
                    f'SUBSTRING(snapshot, 1, 4) = %s AND '
                    f'SUBSTRING(snapshot, 5, 2) = %s '
                    f'GROUP BY snapshot_dt '
                    f'ORDER BY snapshot_dt ASC', 
                    [datasource, dataset, requested_year, requested_month]
        )
    else:
        valid_snapshots = db_operation(conn,
                    f"SELECT snapshot_dt, snapshot "
                    f"FROM validation WHERE is_copied_from_archive = 1 "
                    f"AND data_source = %s AND data_set = %s "
                    f"AND ((LENGTH(snapshot) < 8 AND TO_CHAR(snapshot_dt, 'YYYY') = %s) OR SUBSTRING(snapshot, 1, 4) = %s) "
                    f"AND ((LENGTH(snapshot) < 8 AND TO_CHAR(snapshot_dt, 'MM') = %s) OR SUBSTRING(snapshot, 5, 2) = %s)", 
                    [datasource, dataset, requested_year, requested_year, \
                    requested_month, requested_month]
        )

    print(sorted(valid_snapshots))
    if incremental:
        return sorted(valid_snapshots)
    else:
        return sorted(valid_snapshots)[-1:]
    

def get_snapshot_date(datasource, snapshot_dt, snapshot):
        if str.isdigit(snapshot) and len(snapshot) > 8:
            snapshot = snapshot[0:8]

        if datasource in ['source_emailinsight', 'source_linkage', 'source_cell']:
            if isinstance(snapshot_dt, str):
                snapshot = (datetime.strptime(snapshot_dt, '%Y-%m-%d')).strftime('%Y%m%d')
            elif isinstance(snapshot_dt, datetime):
                snapshot = snapshot_dt.strftime("%Y%m%d")
            else:
                raise Exception(f'Invalid snapshot_dt data type {snapshot_dt}')
        return snapshot
    

@dag(
    schedule_interval='@once',
    start_date=datetime(2023, 1, 1, 0, 0),
    catchup=False,
    tags=['example'],
)
def generate_semaphore():


    @task()
    def generate_semaphore_file():

        db_conn = connect_to_postgres_db()
        env = get_environment()

        context = get_current_context()
        execution_date = context.get('execution_date', '')
        print(execution_date)
        print(type(execution_date))

        run_time_config = yaml.safe_load(Variable.get(f'{team_name}_generate_semaphore_config'))
        root_location = run_time_config['management_root']
        semaphore_file_path_root = run_time_config['semaphore_file_path_root']
        incremental_datasources = run_time_config['incremental_datasources']
        # root_location = 's3://signal-hub-365007379098-us-east-1/us/experian'
        # semaphore_file_path_root = 's3://signal-hub-365007379098-us-east-1/us/experian/semaphore_file'
        # incremental_datasources = ['fraudnet']
        print(root_location, semaphore_file_path_root)
        print(incremental_datasources, type(incremental_datasources))
        requested_snapshot_dt = '202507'
        snapshot_date = datetime.strptime(requested_snapshot_dt, "%Y%m")

        ## Specify business_unit, datasource, dataset for OEIDP cluster generation
        oeidp_datasources = [
            ("cis", "cem", "cem_cell"),
            ("cis", "cem", "cem_clarity"),
            ("cis", "cem", "cem_residential"),
            ("cis", "gvap", "gvap_address"),
            ("cis", "gvap", "gvap_cons_address"),
            ("cis", "gvap", "gvap_cons_name"),
            ("cis", "gvap", "gvap_cons_opt"),
            ("cis", "gvap", "gvap_cons_telephone"),
            ("cis", "rentbureau", "rentbureau"),
            ("ems", "source_cell", "source_cell"),
            ("ems", "source_emailinsight", "ems_eiscore"),
            ("ems", "source_emailinsight", "ems_email"),
            ("ems", "source_linkage", "source_linkage"),
            ("gfid", "fraudnet", "fraudnet_ecna")
        ]

        for (business_unit, datasource, dataset) in oeidp_datasources:
            print(f'====== {business_unit}, {datasource}, {dataset} ======')
            ## Query the inventory DB to collect all snapshots
            valid_snapshots = get_list_of_valid_snapshots(db_conn, datasource, dataset, snapshot_date, incremental=(datasource in incremental_datasources))
            print('valid snapshots:', valid_snapshots)
            if len(valid_snapshots) == 0:
                print(f'No semaphore file for {datasource}, {dataset} and snapshot {snapshot_date}: no data found')
                continue

            ## Create semaphore file, write content into it and upload it to S3
            snapshot_date_yyyymm = f'{str(snapshot_date.year)}{str(snapshot_date.month).zfill(2)}'
            semaphore_file_name = f'{business_unit}.{datasource}.{dataset}.{snapshot_date_yyyymm}.sem'
            local_path = os.path.join(mem_dir, semaphore_file_name)
            handler = open(local_path, 'w+')
            for snapshot in sorted(list(set([x[1][:8] for x in valid_snapshots]))):
                # snapshot = get_snapshot_date(datasource, snapshot_dt, snapshot)
                handler.write(f'{root_location}/{business_unit}/{datasource}/{dataset}/ts={snapshot}/\n')
            handler.close()
            s3_path = os.path.join(semaphore_file_path_root, semaphore_file_name)
            upload_file_from_local_to_s3_using_aws_cli(local_path, s3_path, creds=env, mem_dir=mem_dir)
            print(f'Successfully generated semaphore file for {datasource}, {dataset} and snapshot {snapshot_date}')

        db_conn.close()

    generate_semaphore_file()

semaphore_generation = generate_semaphore()