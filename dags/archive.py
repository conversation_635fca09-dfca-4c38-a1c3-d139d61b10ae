import os
import json
import yaml
import subprocess
from datetime import datetime, timedelta
from airflow.operators.python import get_current_context
from airflow.providers.amazon.aws.hooks.base_aws import AwsBaseHook
from airflow.hooks.base import BaseHook
from airflow.hooks.S3_hook import S3Hook
from airflow.models import Variable
from airflow.decorators import dag, task

from signalhub.datavalidation.utils.utilities import create_connection, db_operation, get_bucket_and_key_from_filename
from signalhub.datavalidation.utils.data_management import run_data_management
from signalhub.datavalidation.utils.validation import run_validation
from signalhub.datavalidation.utils.cleanup import run_cleanup, copy_snapshot_from_archive


team_name = 'datalab'
mem_dir = "/tmp"


def connect_to_postgres_db(**context):
    connection = BaseHook.get_connection(f'{team_name}_postgres_database_connection')
    db_conn = create_connection(connection.host, connection.login, connection.password)
    return db_conn


# def get_aws_credentials():
#     creds = AwsBaseHook(f"{team_name}_aws_conn", client_type="s3").get_credentials()
#     return dict(
#         AWS_ACCESS_KEY_ID=creds.access_key,
#         AWS_SECRET_ACCESS_KEY=creds.secret_key,
#         AWS_SESSION_TOKEN=creds.token,
#     )


def get_environment():
    conn = BaseHook.get_connection()
    print("extra: " + conn.get_extra())
    conn_extra = json.loads(conn.get_extra())
    print("extra loaded: " + str(conn_extra))
    env = os.environ.copy()
    env.update(dict(
        AWS_IAM_ROLE_ASSUME=conn_extra.get("role_arn"),
        AWS_REGION=conn_extra.get("region_name", "us-east-1")
    ))
    return env


def gpg_decrypt(decrypt_script, env):
    cmd = ["bash", decrypt_script]
    process_read_raw = subprocess.Popen(cmd, env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    std_out, std_err = process_read_raw.communicate()
    print('STD_ERR:', std_err)
    print('STD_OUT:', std_out)


@dag(
    schedule_interval='0 15 * * *',
    start_date=datetime(2022, 1, 1, 0, 0),
    #end_date=datetime(2022, 2, 2, 0, 0),
    catchup=False,
    tags=['example'],
)
def data_validation_dag_for_oeidp():


    @task()
    def test():
        print('This is a test')

        db_conn = connect_to_postgres_db()
        res = db_operation(db_conn, 'SELECT count(*) from validation', [])
        print(res)


        context = get_current_context()
        execution_date = context.get('execution_date', '')
        print('EXECUTION_DATE:', execution_date)
        yesterday = (datetime(execution_date.year, execution_date.month, execution_date.day) - timedelta(days=1)).strftime('%Y%m%d')
        print('YESTERDAY:', yesterday)

        env = get_environment()

        print('CREDENTIAL:', env)

        db_conn.close()

        return yesterday


    @task()
    def data_management_step():

        print('====== start data_management ======')

        context = get_current_context()
        db_conn = connect_to_postgres_db()
        boto3_client = S3Hook().get_conn()
        env = get_environment()

        gpg_key = Variable.get(f'{team_name}_gpg_private_key')
        gpg_passphrase = Variable.get(f'{team_name}_gpg_passphrase')

        run_time_config = yaml.safe_load(Variable.get(f'{team_name}_data_management_config'))
        run_time_config['gpg_key'] = gpg_key 
        run_time_config['gpg_passphrase'] = gpg_passphrase
        run_time_config['aws_conn_id'] = None
        run_time_config['credentials'] = env
        run_time_config['mem_dir'] = mem_dir
        run_time_config['ms_teams_channel_url'] = Variable.get(f'{team_name}_ms_teams_channel_url')
        run_time_config['https_proxy_url'] = Variable.get(f'{team_name}_https_proxy_url')
        run_time_config['http_proxy_url'] = Variable.get(f'{team_name}_http_proxy_url')
        run_time_config['start_date'] = '19000101'
        execution_date = context.get('execution_date', '')
        print('EXECUTION_DATE:', execution_date)
        run_time_config['end_date'] = (datetime(execution_date.year, execution_date.month, execution_date.day) - timedelta(days=0)).strftime('%Y%m%d')
        print(run_time_config)
        run_data_management(db_conn, boto3_client, run_time_config)
        db_conn.close()
    

    @task()
    def validation_step():
        db_conn = connect_to_postgres_db()
        config_package = {}
        config_package['validation_config'] = yaml.safe_load(Variable.get(f'{team_name}_validation_config'))
        config_package['interval_dict'] = yaml.safe_load(Variable.get(f'{team_name}_transfer_interval_config'))
        config_package['dataset_dict'] = yaml.safe_load(Variable.get(f'{team_name}_dataset_list_for_data_source_validation'))
        config_package['total_line_count_bootstrap_dict'] = yaml.safe_load(Variable.get(f'{team_name}_total_line_count_bootstrap_config'))
        config_package['fraudnet_alert_list'] = yaml.safe_load(Variable.get(f'{team_name}_fraudnet_alert_config'))
        config_package['ms_teams_channel_url'] = Variable.get(f'{team_name}_ms_teams_channel_url')
        config_package['https_proxy_url'] = Variable.get(f'{team_name}_https_proxy_url')
        config_package['http_proxy_url'] = Variable.get(f'{team_name}_http_proxy_url')
        run_validation(db_conn, config_package)
        db_conn.close()
    

    @task()
    def cleanup_step():
        context = get_current_context()
        db_conn = connect_to_postgres_db()
        s3_hook = S3Hook()
        args = yaml.safe_load(Variable.get(f'{team_name}_cleanup_config'))
        args['aws_conn_id'] = None
        args['ms_teams_channel_url'] = Variable.get(f'{team_name}_ms_teams_channel_url')
        args['https_proxy_url'] = Variable.get(f'{team_name}_https_proxy_url')
        args['http_proxy_url'] = Variable.get(f'{team_name}_http_proxy_url')
        args['start_date'] = '19000101'
        execution_date = context.get('execution_date', '')
        print('EXECUTION_DATE:', execution_date)
        args['end_date'] = (datetime(execution_date.year, execution_date.month, execution_date.day) - timedelta(days=0)).strftime('%Y%m%d')
        args['project'] = 'oeidp'
        print(args)

        processing_date = datetime.strptime(context['ti'].xcom_pull(key='return_value', task_ids='test'), "%Y%m%d").date()
        copy_snapshot_from_archive(db_conn, args, processing_date)
        run_cleanup(db_conn, s3_hook.get_conn(), args)
        db_conn.close()


    test() >> data_management_step() >> validation_step() >> cleanup_step()

data_validation = data_validation_dag_for_oeidp()
