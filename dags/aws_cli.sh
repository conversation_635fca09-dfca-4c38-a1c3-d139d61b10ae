aws ec2 create-key-pair \
    --key-name c51336a \
    --key-type rsa \
    --key-format pem \
    --query "KeyMaterial" \
    --output text > ~/.ssh/c51336a_9098.pem


aws ec2 run-instances\
    --image-id $(aws ec2 describe-images --owners 363353661606 --filters "Name=name,Values=eec_aws_rhel_9*" --query "Images | sort_by(@, &CreationDate)[-1].ImageId" --output text --region us-east-1)\
    --instance-type i3.2xlarge\
    --key-name c51336a\
    --subnet-id subnet-047dff300f106b6aa \
    --security-group-ids sg-0fb1cd8a423fbd5b5 \
    --metadata-options "HttpEndpoint=enabled,HttpTokens=required" \
    --tag-specifications "ResourceType=instance,Tags=[{Key=CostString,Value=1010.US.146.204041},{Key=AppID,Value=16713},{Key=Environment,Value=prd},{Key=Project,Value=PRJ0168817},{Key=Purpose,Value=IAR01885},{Key=CreatedOn,Value=$(date -u +'%Y-%m-%dT%H:%M:%S%z')},{Key=CreatedBy,Value=okta/$(whoami)@gdc.local},{Key=app-name,Value=oeidp-validation}]"