import argparse
import boto3
import json
import logging
import os
import time
import hashlib
from datetime import datetime, timedelta

from signalhub.datavalidation.utils.utilities import *


run_on_airflow = None
ms_teams_channel_url = None
https_proxy_url = None
http_proxy_url = None


def check_removable(conn, boto3_client, args, full_path, file_obj, table, file_encrypted):
    #logging.info(f'Retrieving file entry from inventory ({table})...')

    file_location = full_path
    #if table == 'data' and 'cc1_' in full_path:
    #    file_location = full_path.replace('.bz2', '.bz2.gpg')
    res = db_operation(conn, f'SELECT last_modified_time, file_size, archived_time, '
                             f'archived_destination, remove_time FROM {table} WHERE location=%s',
                       [file_location])

    if not res:
        logging.info(f'Failed to retrieve entry for file {full_path} from inventory or file '
                     f'has not been inventoried yet, skipping...')
        return False

    #logging.info(f'File entry found in inventory ({table})...')

    # Previously assumed that location column is unique for data, checksum, and manifest_file table.
    # This assumption is no longer valid as checksum location is not unique anymore. We therefore
    # check instead that last_modified_time, file_size, archived_time of each row queried
    # conditioned on location is identical.
    try:
        assert res.count(res[0]) == len(res)
    except AssertionError as e:
        error_msg = f'Found inconsistent inventory data for {full_path} in the database: result ' \
                    f'set length = {len(res)}\nSkip removing file...'
        logging.error(error_msg)
        send_notification(f'[ CLEANUP ] {error_msg}', ms_teams_channel_url, https_proxy_url=https_proxy_url, http_proxy_url=http_proxy_url)
        return False

    inv_mtime, inv_file_size, inv_archived_time, archived_destination, remove_time = res[0]
    inv_mtime = inv_mtime.strftime("%Y-%m-%d %H:%M:%S")
    #logging.info(f'{inv_mtime}, {inv_file_size}, {inv_archived_time}, {archived_destination}, {remove_time}')

    if not (archived_destination and inv_archived_time):
        logging.info(f'File {full_path} has not been archived yet, skipping...')
        return False

    logging.info(f'Local file was archived to {archived_destination} at {inv_archived_time}, '
                 f'retrieving stat information about the local file...')

    try:
        #local_mtime = datetime.fromtimestamp(os.path.getmtime(full_path)).replace(microsecond=0)
        #local_file_size = os.path.getsize(full_path)
        local_mtime = file_obj['LastModified'].strftime("%Y-%m-%d %H:%M:%S")
        local_file_size = file_obj['Size']

    except Exception as e:
        logging.warning(f'Failed to retrieve stat information about the local file '
                        f'{full_path}: {e}, aborting...')
        return False

    #logging.info('Successfully retrieved stat information about the local file, '
    #             'now retrieving stat information about the archived file...')

    key_location = archived_destination.replace(f's3://{args["archive_bucket"]}/', '')
    #logging.info(f'file key location: {key_location}')

    try:
        if run_on_airflow:
            archived_info = get_head_object_using_airflow(args["archive_bucket"], key_location, args['aws_conn_id'])
        else:
            archived_info = get_head_object(boto3_client, args["archive_bucket"], key_location)
        #logging.info(f'ARCHIVED INFO: {archived_info}')

    except Exception as e:
        logging.warning(f'Failed to retrieve stat information about the archived '
                        f'file {archived_destination}: {e}, aborting...')
        return False

    # The retrieved head_object must contain both the mtime and size of the archived file
    if not ('LastModified' in archived_info and 'ContentLength' in archived_info):
        logging.warning(f'Head object retrieved from archive is missing key(s): {archived_info}')
        return False

    #logging.info('Successfully retrieved relevant stat information about the archived file, '
    #             'now processing integrity checks...')

    # Check that the local file's current last_modified_time is consistent with that recorded
    # in the inventory
    #logging.info(f'Last modified time: {inv_mtime} (inventory), {local_mtime} (local)')
    if inv_mtime != local_mtime:
        logging.warning(f'Local file {full_path} has been modified since last inventoried, aborting...')
        return False

    # Check that the archived file's current last_modified_time is consistent with the
    # archived_time recorded in the inventory
    archived_file_mtime = archived_info['LastModified'].astimezone().replace(microsecond=0, tzinfo=None)
    #logging.info(f'Archived time: {inv_archived_time} (inventory), {archived_file_mtime} (archive)')
    if inv_archived_time != archived_file_mtime:
        logging.warning(f'Archived file {archived_destination} has been modified since '
                        f'last inventoried, aborting...')
        return False

    # Check for consistency of file sizes to be safe
    #logging.info(f'File size: {local_file_size} (local), {archived_info["ContentLength"]} '
    #             f'(archive), {inv_file_size} (inventory)')
    if not (local_file_size == archived_info['ContentLength'] == inv_file_size):
        logging.warning(f'File size mismatch between local file {full_path}, archived file '
                        f'{archived_destination}, and inventory, aborting...')
        return False

    #logging.info('Successfully confirmed metadata consistency, now checking that the file has '
    #             'been archived for at least the grace period specified...')

    # Check that the file has been archived for longer than the specified grace period
    since_archived = datetime.now() - inv_archived_time
    rem_grace_period = timedelta(hours=args['grace_period']) - since_archived
    logging.info(f'File has been archived for {since_archived} since the recorded archive '
                 f'timestamp of {inv_archived_time}')
    if rem_grace_period > timedelta(0):
        logging.info(f'This file was just recently archived, there is still {rem_grace_period} of '
                     f'the {args["grace_period"]} hour grace period remaining, skipping...')
        return False

    logging.info('Successfully confirmed that the file is ready to be removed.')
    return True


def remove_converted_file(conn, boto3_client, converted_file_location, original_file_location, table, dry_run=False):
    try:
        remove_time = remove_file(boto3_client, converted_file_location, dry_run)
        #remove_time = datetime.now().replace(microsecond=0)
        logging.info(f'Converted file is removed at {remove_time}')
    except:
        logging.warning('Failed to remove the converted file...')
        return

    if remove_time:
        try:
            #logging.info(f'Recording removal time of converted file: {remove_time}')
            db_operation(conn,
                         f'UPDATE {table} SET converted_file_remove_time=%s WHERE location=%s',
                         [remove_time, original_file_location])
        except Exception as e:
            error_msg = f'Error updating database remove time for converted file {converted_file_location}: ' \
                        f'{e}\nTerminating cleanup...'
            logging.error(error_msg)
            send_notification(f'[ CLEANUP ] {error_msg}', ms_teams_channel_url, https_proxy_url=https_proxy_url, http_proxy_url=http_proxy_url)
            exit(1)


def remove_original_file(conn, boto3_client, original_file_location, table, args):
    logging.info(f'Removing local copy at {original_file_location} to free up space...')
    try:
        remove_time = remove_file(boto3_client, original_file_location)
        #remove_time = datetime.now().replace(microsecond=0)
        logging.info(f'Original file is removed at {remove_time}')
    except:
        logging.warning('Failed to remove the original file...')
        return

    if remove_time:
        try:
            #logging.info(f'Recording removal time: {remove_time}')
            db_operation(conn, f'UPDATE {table} SET remove_time=%s WHERE location=%s',
                         [remove_time, original_file_location])

            # Special handle CEM manifest files which contain CRC checksum(s)
            if table == args['manifest_file_table_name'] and os.path.basename(original_file_location).startswith('manifest'):
                db_operation(conn, f'UPDATE {args["checksum_table_name"]} SET remove_time=%s WHERE location=%s',
                             [remove_time, original_file_location])

        # If write permission to the db somehow expires in the middle of clean
        # up, immediately terminate execution.
        except Exception as e:
            error_msg = f'Error updating database remove time for file {original_file_location}: ' \
                        f'{e}\nTerminating cleanup...'
            logging.error(error_msg)
            send_notification(f'[ CLEANUP ] {error_msg}', ms_teams_channel_url, https_proxy_url=https_proxy_url, http_proxy_url=http_proxy_url)
            exit(1)


def check_missing(conn, boto3_client, file_type, table):
    """Check for missing files that were not removed by this cleanup script.

    This method can be called after cleanup to identify all files that were not removed by the cleanup
    script but that are missing from the landing zone.
    """
    logging.info(f'Checking for missing {file_type} files against {table} table...')

    to_check = db_operation(conn, f'SELECT location FROM {table} WHERE remove_time IS NULL', [])
    print(len(to_check))

    miss_cnt = 0
    for full_path in to_check:
        bucket, key = get_bucket_and_key_from_filename(full_path)
        file_obj = boto3_client.get_object(Bucket=bucket, Key=key)
        #if not os.path.isfile(full_path):
        # if this is a file, not a directory
        if file_obj['ContentLength'] > 0:
            logging.warning(f'Missing file identified: {full_path}')
            miss_cnt += 1

    if miss_cnt > 0:
        logging.warning(f'There are {miss_cnt} {file_type} files missing from landing zone.')
    else:
        logging.info(f'There are no {file_type} files missing from landing zone.')


def get_original_file(conn, file_path, args):
    # Query the list to get the original file location
    #logging.info(f'Get orginal file for {file_path}')
    original_file_location = db_operation(conn,
                        f'SELECT '
                            f'location '
                        f'FROM '
                            f'{args["data_table_name"]} '
                        f'WHERE '
                            f'converted_file_location=%s '
                            f'AND bz2_file_archived_destination=%s;'
                        ,
                        [file_path, file_path.replace(args["dirpath"], args["archive_root"])])

    # If the file is a converted file, will return a list of tuples containing the location of the original file.
    # Else it will return an empty list
    return original_file_location[0][0] if len(original_file_location) > 0 else None


def check_data_validity(conn, data_file, is_converted_file=False):
    logging.info(f'Checking validity of data file {data_file}...')

    ## Always consider data files from certain data sources as valid since they do not provide manifest or checksum files
    exemption_data_sources = ['da/PID']
    if any(x in data_file for x in exemption_data_sources):
        return True

    data_file_column = 'converted_file_location' if is_converted_file else 'location'
    res = db_operation(conn, f"SELECT location, checksum_validated, line_cnt_validated FROM data WHERE {data_file_column} = '{data_file}'", [])

    if not res:
        logging.info(f'Failed to retrieve entry for file {data_file} from inventory or file '
                     f'has not been inventoried yet, skipping...')
        return -1

    checksum_validated, line_cnt_validated = res[0][1], res[0][2]
    #logging.info(f'validation check: {checksum_validated}, {line_cnt_validated}')

    #is_valid = ((checksum_validated == 1 or checksum_validated is None) and (line_cnt_validated == 1 or line_cnt_validated is None))
    if checksum_validated == 1 and line_cnt_validated == 1:
        return 1
    elif checksum_validated == 0 or line_cnt_validated == 0:
        logging.warning(f'Data file is not valid: checksum_validity: {checksum_validated}, line count validity: {line_cnt_validated}')
        return 0
    else:
        logging.warning(f'Data file validity not fully determined: checksum_validity: {checksum_validated}, line count validity: {line_cnt_validated}')
        return -1


def get_last_day_of_month(date):
    import calendar
    last_day = calendar.monthrange(date.year, date.month)[1]
    return datetime(date.year, date.month, last_day).date().strftime('%Y%m%d')


product_name_mapping = {
    'FIS_PID': 'PID',
    'cc1': 'crosscore_v1.x'
}

def copy_data_file_to_management_bucket(boto3_client, full_path, file_obj, args):
    '''
    need to determine location in the management bucket:
        + for signal hub: /{root_location}/{bu}/{product}/individual_source={client_name}/type=raw/ts={ts}
        + for oeidp: /{root_location}/{bu}/{data_source}/ts={ts}
    note:
        + for signal hub: client_name is hased for FN and CC
        + for both signal hub and oeidp, ts is in 'yyyyMMdd' format
    need to strip the suffix after file type in the filename
    '''

    def remove_suffix_in_filename(filename):
        return filename.split('.bz2')[0] + '.bz2'

    logging.info(f'Attempting to copy {full_path} to management bucket')

    base_metadata = extract_metadata_base(full_path, file_obj)
    metadata_from_file_name = extract_metadata_from_file_name(full_path)
    metadata = {**base_metadata, **metadata_from_file_name}
    bu = metadata['business_unit']
    data_source = metadata['data_source']
    data_set = metadata['data_set']
    #snapshot = metadata['snapshot'][:8]
    snapshot = get_last_day_of_month(metadata['snapshot_dt'])
    root_location = args['management_root'].replace(f's3://{args["management_bucket"]}/', '')

    file_name = remove_suffix_in_filename(os.path.basename(full_path))
    product = metadata['data_source']
    if product == 'fraudnet' and 'feedback' in file_name:
        product = 'raw_fraudnet_feedback'
    if product == 'PID':
        if data_set == 'FIS_PID_TRAN':
            product = 'PID'
        if data_set == 'PID_KIQ':
            product = 'PID_KIQ'
        if data_set == 'CSTL':
            product = 'CM_SCORE'

    if args['project'] == 'signal_hub':
        logging.info('For signal hub data sources')
        if data_source.startswith('fraudnet'):
            client_name = hashlib.sha256(data_set.split('_')[1].encode()).hexdigest()[0:10]
        elif data_source.startswith('crosscore_v1'):
            client_name = file_name.split('_')[1]
        elif data_source.startswith('PID'):
            client_name = product
        elif data_source.startswith('cem'):
            client_name = file_name.split('.')[0].split('_')[1]
        elif data_set:
            client_name = data_set
        else:
            logging.warning('Could not determine client name, skip moving it to management bucket...')
            return
        logging.info(f'client_name: {client_name}')
        destination_key = f'{root_location}/{bu}/{product}/individual_source={client_name}/type=raw/ts={snapshot}/{file_name}'
        # ======= new process: to copy files to temporary location as well to support daily process =======
        temp_destination_key = None
        products_to_move_to_temp_location = ['crosscore_v1.x', 'fraudnet', 'raw_fraudnet_feedback', 'PID']
        if product in products_to_move_to_temp_location:
            temp_root_location = args['management_root_temp'].replace(f's3://{args["management_bucket"]}/', '')
            temp_destination_key = f'{temp_root_location}/{bu}/{product}/individual_source={client_name}/type=raw/ts={snapshot}/{file_name}'
        # ==========================
    else: 
        destination_key = f'{root_location}/{bu}/{data_source}/ts={snapshot}/{file_name}'

    source_bucket, source_key = get_bucket_and_key_from_filename(full_path)
    if run_on_airflow:
        copy_file_on_s3_using_airflow(source_bucket, source_key, args['management_bucket'], destination_key)
        # ======= new process: to copy files to temporary location as well to support daily process =======
        if temp_destination_key is not None:
            copy_file_on_s3_using_airflow(source_bucket, source_key, args['management_bucket'], temp_destination_key)
        # ==========================
    else:
        copy_file_on_s3(source_bucket, source_key, args['management_bucket'], destination_key)
    logging.info('Copied to s3://{0}/{1}'.format(args['management_bucket'], destination_key))


def cleanup_cc2_file(boto3_client, full_path, file_obj, args):
    '''
    Just need to move CC2 files to valid bucket
    '''
    snapshot = get_last_day_of_month(file_obj['LastModified'])
    logging.info(f'snapshot: {snapshot}')
    source_bucket, source_key = get_bucket_and_key_from_filename(full_path)
    file_name = os.path.basename(full_path)
    root_location = args['management_root'].replace(f's3://{args["management_bucket"]}/', '')
    bu = 'gfid'
    destination_key = f'{root_location}/{bu}/crosscore_v2.x/individual_source=all/type=raw/ts={snapshot}/{file_name}'
    copy_file_on_s3_using_airflow(source_bucket, source_key, args['management_bucket'], destination_key)

    # ======= new process: copy CC2 files to sh_workspace as well and partition by date =======
    snapshot_date = file_obj['LastModified'].strftime('%Y%m%d')
    snapshot_month = file_obj['LastModified'].strftime('%Y%m')
    #destination_key = f'sh_workspace/crosscore_v2.x/individual_source=all/type=raw/ts={snapshot}/{file_name}'
    cc2_root_location = args['management_root_temp'].replace(f's3://{args["management_bucket"]}/', '')
    cc2_destination_key = f'{cc2_root_location}/gfid/crosscore_v2.x/individual_source=all/type=raw/month={snapshot_month}/ts={snapshot_date}/{file_name}'
    copy_file_on_s3_using_airflow(source_bucket, source_key, args['management_bucket'], cc2_destination_key)
    # ==========================

    try:
        remove_time = remove_file(boto3_client, full_path)
        logging.info('Original file is removed')
    except:
        logging.warning('Failed to remove the original file...')


def cleanup(conn, boto3_client, args):

    bucket, prefix = get_bucket_and_key_from_filename(args['dirpath'])
    logging.info(f'Traversing {bucket}/{prefix}')
    #content_list = boto3_client.list_objects(Bucket=bucket, Prefix=prefix)['Contents']
    #print(len(content_list))

    paginator = boto3_client.get_paginator('list_objects_v2')
    pages = paginator.paginate(Bucket=bucket, Prefix=prefix)
    all_content_list = []
    for page in pages:
        all_content_list += page['Contents']
    all_content_list = [obj for obj in all_content_list if os.path.basename(obj['Key']) != '']
    logging.info(f'# files in total: {len(all_content_list)}')

    content_list = all_content_list
    logging.info(f'# files to process: {len(content_list)}')

    num_file_cleaned = 0

    for obj in content_list:

        full_path = f's3://{bucket}/' + obj['Key']

        # skip irrelevant files
        identifiers_of_files_to_exclude = ['keep', '.txt', '.inprogress', 'crosscore_v2.x/test/', 'third_party', 'adhoc', 'laas_output']
        if any(x in full_path for x in identifiers_of_files_to_exclude) and '.manifest' not in full_path:
            continue

        if 'crosscore_v2.x/' in full_path and all([x not in full_path for x in ['prod/', 'prod_op/']]):
            continue

        # skip the file that came in today
        #landing_date_time = obj['LastModified']
        #landing_date = f'{landing_date_time.year}{str(landing_date_time.month).zfill(2)}{str(landing_date_time.day).zfill(2)}'
        receiving_date = get_receiving_date(full_path, obj)
        if not receiving_date:
            logging.warning(f'Cannot determine receiving date for {full_path}.')
            continue

        if receiving_date < args['start_date'] or receiving_date > args['end_date']:
            continue

        # skip object that is a directory
        if obj['Size'] == 0:
            continue

        #if num_file_cleaned > 1000000:
        #    break

        logging.info('------------------------------------')
        logging.info(f'Processing file: {full_path}')

        num_file_cleaned += 1

        ## clean up CC2 file
        if 'crosscore_v2' in full_path:
            cleanup_cc2_file(boto3_client, full_path, obj, args)
            continue

        file_name = os.path.basename(full_path)
        is_checksum = any(x in file_name for x in ['.md5', '.chksum', '.crc32sum', '.checksum'])
        is_manifest = ('.manifest' in file_name or file_name.startswith('manifest'))
        is_data = any(x in file_name for x in ['.bz2', '.gz', '.pkzip', '.zip'])

        original_file_location = None
        if is_checksum: 
            table = args['checksum_table_name']
        elif is_data: 
            table = args['data_table_name']
            if '.bz2' in file_name:
                original_file_location = get_original_file(conn, full_path, args) # equals None if it is not a converted bz2
                logging.info(f'original_file_location: {original_file_location}')
        elif is_manifest: 
            table = args['manifest_file_table_name']
        else:
            logging.info('File is not a data, checksum, or manifest file, skipping...')
            continue

        if original_file_location:
            # this is a converted bz2 data file, no need to check removability
            logging.info('Removing converted data file...')
            if args['project'] == 'oeidp':
                remove_converted_file(conn, boto3_client, full_path, original_file_location, table)
            else:
                if check_data_validity(conn, full_path, is_converted_file=True) == 1:
                    copy_data_file_to_management_bucket(boto3_client, full_path, obj, args)
                    remove_converted_file(conn, boto3_client, full_path, original_file_location, table)
                else:
                    logging.warning('This file is not valid!')
        elif '.bz2' in file_name and '.gpg' not in file_name and '.pgp' not in file_name and not is_checksum:
            # this is an original bz2 data file
            logging.info('Removing original data file...')
            removable = check_removable(conn, boto3_client, args, full_path, obj, table, False)
            if args['project'] == 'oeidp':
                if removable:
                    remove_original_file(conn, boto3_client, full_path, table, args)
                else:
                    logging.warning('This file is not removable!')
            else:
                validity = check_data_validity(conn, full_path)
                logging.info(f'This file is is valid? {validity}')
                if removable and validity == 1:
                    copy_data_file_to_management_bucket(boto3_client, full_path, obj, args)
                    remove_original_file(conn, boto3_client, full_path, table, args)
                else:
                    logging.warning('This file is not removable or not validated!')
        else:
            logging.info('Removing other files...')
            # this is a manifest file, checksum file, or an original non-bz2 data file
            if check_removable(conn, boto3_client, args, full_path, obj, table, False):
                remove_original_file(conn, boto3_client, full_path, table, args)
            else:
                logging.warning('This file is not removable!')

    print(f'# files cleaned up: {num_file_cleaned}')


def is_snapshot_valid(data_source, data_set, integrity_validated, line_cnt_validated, checksum_validated):

    # copy these datasets regardless of validation outcome as they are not used by OEIDP but constantly exhibit validation issue due to operational error
    if data_set in ['cem_coa', 'ems_llphone', 'gvap_employment']:
        return True

    # skip line count validation due to extremely large data size (auto/nvdb, ems_email) or line count was not provided in manifest file (source_cell)
    if data_set in ['nvdb', 'ems_email', 'source_cell']:
        return (integrity_validated == 1) and (checksum_validated == 1)

    # skip checksum validation due to checksum file was not provided
    if data_set in ['clickstream', 'membership'] or data_set.startswith('creditheader'):
        return (integrity_validated == 1) and (line_cnt_validated == 1)

    # for all other data sources, need to pass all validations
    return (integrity_validated == 1) and (line_cnt_validated == 1) and (checksum_validated == 1)


def get_list_of_valid_snapshots(conn, processing_date):

    ## All data sources except Fraudnet
    #uncopied_snapshots = db_operation(conn,
    #             f'SELECT data_source, data_set, SUBSTRING(snapshot, 1, 8) AS snapshot, '
    #             f'snapshot_dt, manifest_location, '
    #             f'integrity_validated, line_cnt_validated, checksum_validated '
    #             f'FROM validation WHERE is_copied_from_archive = 0 and data_source != "fraudnet"', []
    #)

    uncopied_snapshots = db_operation(conn,
                 f"SELECT data_source, data_set, snapshot, "
                 f"snapshot_dt, manifest_location, "
                 f"integrity_validated, line_cnt_validated, checksum_validated "
                 f"FROM validation WHERE DATE(receiving_time) = %s AND data_source != 'fraudnet'", [processing_date]
    )

    valid_snapshots = []
    for data_source, data_set, snapshot, snapshot_dt, manifest_location, integrity_validated, line_cnt_validated, checksum_validated in uncopied_snapshots:

        if is_snapshot_valid(data_source, data_set, integrity_validated, line_cnt_validated, checksum_validated):
            valid_snapshots.append((manifest_location, snapshot_dt, snapshot))

    ## For Fraudnet
    sql_query = '''
            SELECT null, snapshot_dt, SUBSTRING(snapshot, 1, 8) AS snapshot FROM data
                    WHERE data_source = 'fraudnet' AND
                          DATE(last_modified_time) = %s
                GROUP BY snapshot_dt, SUBSTRING(snapshot, 1, 8)
        '''
    valid_snapshot_fraudnet = db_operation(conn, sql_query, [processing_date])

    return valid_snapshots + valid_snapshot_fraudnet


def get_list_of_bz2_files(conn, manifest_location, snapshot):

    if manifest_location is not None: # All data sources except Fraudnet
        bz2_query = '''
                SELECT archived_destination, bz2_file_archived_destination, snapshot_dt FROM manifest_info, data
                WHERE manifest_info.data_key = data.data_key AND manifest_info.location = %s
            '''
        return db_operation(conn, bz2_query, [manifest_location])
    else: # For Fraudnet
        bz2_query = '''
                SELECT archived_destination, bz2_file_archived_destination, snapshot_dt FROM data
                WHERE checksum_validated = 1 AND data_source = 'fraudnet' AND SUBSTRING(snapshot, 1, 8) = %s
            '''
    return db_operation(conn, bz2_query, [snapshot])


def determine_destination(full_path, args):

    def remove_suffix_in_filename(filename):
        return filename.split('.bz2')[0] + '.bz2'

    root_location = args['management_root'].replace(f's3://{args["management_bucket"]}/', '')
    base_metadata = extract_metadata_base(full_path, None)
    metadata_from_file_name = extract_metadata_from_file_name(full_path)
    metadata = {**base_metadata, **metadata_from_file_name}
    bu = metadata['business_unit']
    data_source = metadata['data_source']
    data_set = metadata['data_set']
    snapshot = metadata['snapshot'][:8]
    file_name = remove_suffix_in_filename(os.path.basename(full_path))

    return f'{root_location}/{bu}/{data_source}/{data_set}/ts={snapshot}/{file_name}'


def copy_snapshot_from_archive(conn, args, processing_date):

    '''
    From the inventory DB, get the list of valid snapshots for all data sources and datasets:
        - Integrity, line count and checksum need to be validated
        - Apply exceptions for certain data sources
        - Not copied before (is_copied_from_archive = 0)
    Get the list of bz2 files for each data source + dataset + snapshot
    Copy each file to the management bucket with appropriate directories
    '''

    valid_snapshots = get_list_of_valid_snapshots(conn, processing_date)
    copied_snapshot = []
    for manifest_location, snapshot_dt, snapshot in valid_snapshots:
        if (manifest_location, snapshot) in copied_snapshot:
            continue
        bz2_files = get_list_of_bz2_files(conn, manifest_location, snapshot)
        for archived_destination, bz2_file_archived_destination, _ in bz2_files:
            ## copy file from archive to appropriate directory in management bucket
            source = bz2_file_archived_destination if bz2_file_archived_destination is not None else archived_destination
            destination_key = determine_destination(source, args)
            source_bucket, source_key = get_bucket_and_key_from_filename(source)
            print(f'Copying {source} ==> {destination_key}')
            copy_file_on_s3_using_airflow(source_bucket, source_key, args['management_bucket'], destination_key)
        copied_snapshot.append((manifest_location, snapshot))

        ## update is_copied_from_archive flag to 1 if copied from archive
        print(f'****** RESET LABEL for {manifest_location}, {snapshot_dt} ******')
        db_operation(conn, f'UPDATE validation SET is_copied_from_archive = 1 '
                           f'WHERE manifest_location = %s AND snapshot_dt = %s AND snapshot = %s', 
                     [manifest_location, snapshot_dt, snapshot])


def run_cleanup(conn, boto3_client, args, run_on_airflow_flag=True, dry_run_flag=False):

    global run_on_airflow
    run_on_airflow = run_on_airflow_flag
    global ms_teams_channel_url, https_proxy_url, http_proxy_url
    ms_teams_channel_url = args['ms_teams_channel_url']
    https_proxy_url = args['https_proxy_url']
    http_proxy_url = args['http_proxy_url']

    # set up logging
    create_local_directory(args['log_dir'])
    if not run_on_airflow_flag:
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
    start = time.time()
    logging.basicConfig(filename='%s/cleanup.log.%s'
                                 % (args['log_dir'], datetime.utcfromtimestamp(start).strftime('%Y%m%d')),
                        level=logging.DEBUG, filemode='a', format='%(asctime)s %(name)s - %(levelname)s - %(message)s',
                        datefmt='%Y-%m-%d %H:%M:%S')
    config_logging_level(logging)
    logging.info('====== Start cleanup.py ======')

    logging.info(f'Initiating landing zone cleanup: {args["dirpath"]}')
    cleanup(conn, boto3_client, args)

    #logging.info('Cleanup process finished, now checking for missing files in landing zone...')

    #check_missing(conn, boto3_client, 'data', args['data_table_name'])
    #check_missing(conn, boto3_client, 'checksum', args['checksum_table_name'])
    #check_missing(conn, boto3_client, 'manifest', args['manifest_file_table_name'])

    elapsed = time.time() - start
    logging.info(f'[RUN TIME STATS]: Running cleanup.py against {args["dirpath"]} took '
                 f'{elapsed} seconds.')


if __name__ == '__main__':

    print('====== start ======')
    parser = argparse.ArgumentParser(description='Execute cleanup against landing zone '
                                                 'based on the input config file.')
    parser.add_argument('--config', dest='config_file_path', required=True,
                        help='Path to the YAML file specifying the cleanup configuration.')
    parser.add_argument('--dry-run', dest='dry_run_flag', action='store_true',
                        help='Boolean value indicating if this is a dry run. Dry runs '
                             'will generate logs but not actually remove files.')
    args = parser.parse_args()

    postgres_db_config = {'hostname': 'datavalidation-zsddkcgtk35qfxw9.c9qafslrwnv1.us-east-1.rds.amazonaws.com', 'login':'postgres', 'password':'8SdnH28TDeEx6n2NSQVALIDwCVxq2osFaT4DIc762hyKVF8glqk2KfQRRMQilnDK'}
    db_conn = create_connection(postgres_db_config['hostname'], postgres_db_config['login'], postgres_db_config['password'])

    boto3_client = boto3.client('s3', aws_access_key_id='********************', aws_secret_access_key='E3A79gAqC5Au4SbW+YcvdRiVG3W6nQio1tMW5Uje', region_name='us-east-1')

    from utilities import parse_yaml_config_file
    config = parse_yaml_config_file(args.config_file_path)
    config['ms_teams_channel_url'] = 'https://experian.webhook.office.com/webhookb2/4c18cc76-119c-4521-abe0-ef939bd1e583@be67623c-1932-42a6-9d24-6c359fe5ea71/IncomingWebhook/61d4794e7cfd4c83b2da230c1988b799/178ebd7c-3a5f-4b4b-9ce5-ab63bfd1947b'
    config['https_proxy_url'] = 'http://************:9090'
    config['http_proxy_url'] = 'http://************:9090'
    print(config)

    run_cleanup(db_conn, boto3_client, config, run_on_airflow_flag=False, dry_run_flag=args.dry_run_flag)
