import argparse
import logging
import os
import time
import json
from datetime import date, datetime

from signalhub.datavalidation.utils.utilities import create_connection, db_operation, ensure_single_instance, config_logging_level, parse_yaml_config_file, format_timestamp, parse_yaml_config_file, create_boto3_client_on_airflow, create_local_directory, parse_config_file_from_s3
from signalhub.datavalidation.utils.alert import process_alerting

#from utilities import create_connection, db_operation, ensure_single_instance, config_logging_level, parse_yaml_config_file, format_timestamp, parse_yaml_config_file, create_boto3_client_on_airflow, create_local_directory, parse_config_file_from_s3
#from alert import process_alerting

SIGNATURE = 'qMiSlDGNu6gH9XPsgCJN'


def create_manifest_tmp_table(connection):
    # Dataset level validation

    # Create a temporary table storing information about the latest manifest and data files from
    # the perspective of the manifest files; i.e. if a data file is not referenced by any of the
    # manifest files, it will not appear in this table, and if it is referenced by a manifest file
    # but does not exist in the db, it will appear as NULL.
    logging.info('Creating manifest_tmp table...')
    db_operation(connection, 'DROP TABLE IF EXISTS manifest_tmp', [])
    db_operation(connection,
        'CREATE TABLE manifest_tmp AS '
        'SELECT '
            'manifest_latest.location AS manifest_location, '
            'manifest_latest.manifest_key AS manifest_key, '
            'manifest_latest.data_key AS data_key, '
            'manifest_latest.data_source AS data_source, '
            'manifest_latest.data_set AS data_set, '
            'manifest_latest.snapshot AS snapshot, '
            'manifest_latest.snapshot_dt AS snapshot_dt, '
            'manifest_latest.line_cnt AS line_cnt, '
            'manifest_latest.manifest_xfer_time AS manifest_xfer_time, '
            'data_latest.location AS data_location, '
            'data_latest.data_xfer_time AS data_xfer_time, '
            'data_latest.last_modified_time AS data_last_modified_time '
        'FROM '
            '('
                'SELECT '
                    'i.location, '
                    'i.data_key, '
                    'i.line_cnt, '
                    'f.data_source, '
                    'f.data_set, '
                    'f.snapshot, '
                    'f.snapshot_dt, '
                    'f.manifest_xfer_time, '
                    'f.manifest_key '
                'FROM '
                    'manifest_info AS i '
                    'INNER JOIN '
                        '('
                            'SELECT '
                                'manifest_key, '
                                'MAX(xfer_time) AS manifest_xfer_time, '
                                'MAX(location) AS location, '
                                'MAX(data_source) AS data_source, '
                                'MAX(data_set) AS data_set, '
                                'MAX(snapshot) AS snapshot, '
                                'MAX(snapshot_dt) AS snapshot_dt '
                            'FROM '
                                'manifest_file '
                            'GROUP BY '
                                'manifest_key'
                        ') '
                        'AS f '
                        'ON i.location = f.location'
        ') '
        'AS manifest_latest '
        'LEFT JOIN '
            '('
                'SELECT '
                    'data_key, '
                    'MAX(xfer_time) AS data_xfer_time, '
                    'MAX(last_modified_time) AS last_modified_time, '
                    'MAX(location) AS location '
                'FROM '
                    'data '
                'GROUP BY '
                    'data_key'
                ') '
            'AS data_latest '
        'ON manifest_latest.data_key = data_latest.data_key', [])

    db_operation(connection,
        'CREATE INDEX search_index2 ON manifest_tmp (data_source, data_set, snapshot, snapshot_dt)', [])
    db_operation(connection,
        'CREATE INDEX search_index3 ON manifest_tmp (manifest_location, data_key)', [])
    db_operation(connection,
        'CREATE INDEX search_index4 ON manifest_tmp (data_location)', [])
    db_operation(connection,
        'CREATE INDEX search_index5 ON manifest_tmp (data_xfer_time)', [])
    db_operation(connection,
        'CREATE INDEX search_index6 ON manifest_tmp (data_key)', [])

def validate_inventory(connection, args):

    # Create tmp table manifest_tmp
    create_manifest_tmp_table(connection) 

    # Helper CTE for getting the path of all latest data files of a particular latest manifest file
    latest_manifest_files_cte = (
        'WITH manifest_latest_data_files AS (SELECT manifest_tmp.data_location '
        'FROM manifest_tmp, validation WHERE manifest_tmp.data_source = validation.data_source '
        'AND manifest_tmp.data_set = validation.data_set AND manifest_tmp.snapshot = '
        'validation.snapshot)')

    #latest_manifest_files_cte = (
    #    'WITH manifest_latest_data_files AS (SELECT data_location FROM manifest_tmp '
    #    'WHERE manifest_location = (SELECT manifest_tmp.manifest_location '
    #    'FROM manifest_tmp, validation WHERE manifest_tmp.data_source = validation.data_source '
    #    'AND manifest_tmp.data_set = validation.data_set AND manifest_tmp.snapshot = '
    #    'validation.snapshot ORDER BY manifest_tmp.manifest_xfer_time DESC LIMIT 1))')

    #latest_manifest_files_cte = (
    #    'WITH manifest_latest_data_files AS SELECT data_location FROM (SELECT data_location, '
    #    'DENSE_RANK() OVER (PARTITION BY data_source, data_set, snapshot, snapshot_dt '
    #    'ORDER BY manifest_xfer_time DESC) AS rn FROM (SELECT manifest_tmp.* FROM '
    #    'manifest_tmp, validation WHERE manifest_tmp.data_source = validation.data_source AND '
    #    'manifest_tmp.data_set = validation.data_set AND manifest_tmp.snapshot = validation.snapshot) AS t) '
    #    'AS f WHERE rn = 1)')

    # Helper SQL substring to check manifest_location before further validations are carried out
    check_corresponding_manifest = 'WHEN validation.manifest_location IS NULL THEN NULL'
    match_manifest_tmp_with_validation = 'manifest_tmp.data_source = validation.data_source AND manifest_tmp.data_set = validation.data_set AND manifest_tmp.snapshot = validation.snapshot'

    # Given the current latest manifest files and data files, populate the 'missing' column of the
    # manifest info table based on whether each data key referenced by each manifest file exists
    # in the data table.
    logging.info('------ Starting file level validation: missing data files ------')
    db_operation(connection,
        'UPDATE manifest_info SET missing = CAST(NOT EXISTS (SELECT location FROM data '
        'WHERE manifest_info.data_key = data.data_key) AS INTEGER)', [])

    # Create entries in validation table for each unique(data_source, data_set, snapshot, snapshot)
    logging.info('------ Creating entries in validation table ------')
    db_operation(connection,
        'INSERT INTO validation (data_source, data_set, snapshot, snapshot_dt) '
        'SELECT DISTINCT data_source, data_set, snapshot, snapshot_dt FROM manifest_tmp '
        'WHERE snapshot IS NOT NULL AND snapshot_dt IS NOT NULL '
        'ON CONFLICT (data_source, data_set, snapshot, snapshot_dt) DO NOTHING', [])

    logging.info('------ Finding corresponding manifest files ------')
    #db_operation(connection,
    #    'UPDATE validation SET manifest_location = CASE WHEN (SELECT COUNT(DISTINCT '
    #    "SUBSTR(manifest_key, 1, POSITION('.' IN manifest_key) - 1)) FROM manifest_tmp WHERE "
    #    'manifest_tmp.data_source = validation.data_source AND manifest_tmp.data_set = '
    #    'validation.data_set AND manifest_tmp.snapshot = validation.snapshot) != 1 '
    #    'THEN NULL ELSE (SELECT MAX(manifest_location) FROM manifest_tmp '
    #    'WHERE manifest_tmp.data_source = validation.data_source AND manifest_tmp.data_set = '
    #    'validation.data_set AND manifest_tmp.snapshot = validation.snapshot '
    #    'GROUP BY data_source, data_set, snapshot) END', [])

    db_operation(connection,
        'UPDATE validation SET manifest_location = CASE WHEN (SELECT COUNT(DISTINCT '
        "SUBSTR(manifest_key, 1, POSITION('.' IN manifest_key) - 1)) FROM manifest_tmp WHERE "
        'manifest_tmp.data_source = validation.data_source AND manifest_tmp.data_set = '
        'validation.data_set AND manifest_tmp.snapshot = validation.snapshot) != 1 '
        'THEN NULL ELSE (SELECT manifest_location FROM manifest_tmp '
        'WHERE manifest_tmp.data_source = validation.data_source AND manifest_tmp.data_set = '
        'validation.data_set AND manifest_tmp.snapshot = validation.snapshot '
        'ORDER BY manifest_tmp.manifest_xfer_time DESC LIMIT 1) END', [])

    # Given the current latest manifest files and data files, enumerate all distinct combinations
    # of data_source, data_set, snapshot, snapshot_dt into the validation table and populate
    # whether each group has missing data files (as indicated by a NULL data_location in
    # manifest_tmp). If there is ambiguity in which manifest file corresponds to a particular
    # datasource, dataset, snapshot, i.e. there are more than one manifest_files per datasource,
    # dataset, snapshot that do not share a manifest_key or dataset as extracted from the filename,
    # then integrity_validated is set to NULL.
    logging.info('------ Starting dataset level validation: integrity_validated ------')
    #db_operation(connection,
    #    '{} UPDATE validation SET integrity_validated = CASE {} ELSE CAST(NOT EXISTS ('
    #    'SELECT data_location FROM manifest_latest_data_files WHERE data_location IS NULL) AS INTEGER) END'.format(
    #        latest_manifest_files_cte, check_corresponding_manifest), [])
    db_operation(connection,
        'UPDATE validation SET integrity_validated = CASE {} ELSE CAST(NOT EXISTS ('
        'SELECT data_location FROM manifest_tmp WHERE {} AND data_location is NULL) AS INTEGER) END'.format(check_corresponding_manifest, match_manifest_tmp_with_validation), [])

    # For each unique combination of data_source, data_set, snapshot, snapshot_dt as given by each
    # row in the validation table, group by those columns in manifest_tmp and find the latest
    # mtime of all the data files in that group - set that to be the receiving_time for each group.
    logging.info('------ Starting dataset level validation: receiving_time ------')
    #db_operation(connection,
    #    '{} UPDATE validation SET receiving_time = CASE {} ELSE (SELECT MAX(data_last_modified_time) '
    #    'FROM manifest_tmp WHERE manifest_tmp.data_location IN (SELECT data_location from manifest_latest_data_files)) END'.format(
    #        latest_manifest_files_cte, check_corresponding_manifest), [])
    db_operation(connection,
        'UPDATE validation SET receiving_time = CASE {} ELSE (SELECT MAX(data_last_modified_time) '
        'FROM manifest_tmp WHERE {}) END'.format(check_corresponding_manifest, match_manifest_tmp_with_validation), [])

    # For each data file, find its corresponding (latest) manifest file - set that to be its
    # line_cnt_against, compare the line count of each data file as recorded in the data table
    # against the line count value of line_cnt_against as recorded in the manifest_info table -
    # set that to be its line_cnt_validated
    logging.info('------ Starting file level validation: line_cnt_against, line_cnt_validated ------')
    db_operation(connection,
        'UPDATE data SET line_cnt_against = (SELECT manifest_location '
        'FROM manifest_tmp WHERE manifest_tmp.data_key = data.data_key '
        'ORDER BY manifest_tmp.manifest_xfer_time DESC LIMIT 1)', [])

    db_operation(connection,
        'UPDATE data SET line_cnt_validated = CASE '
        'WHEN (SELECT manifest_tmp.line_cnt FROM manifest_tmp WHERE manifest_tmp.manifest_location = '
        'data.line_cnt_against AND manifest_tmp.data_key = data.data_key) IS NULL OR data.line_cnt '
        'IS NULL THEN NULL WHEN (SELECT manifest_tmp.line_cnt FROM manifest_tmp '
        'WHERE manifest_tmp.manifest_location = data.line_cnt_against AND manifest_tmp.data_key = '
        'data.data_key) = data.line_cnt THEN 1 ELSE 0 END', [])

    # For each group, aggregate over its data files and check that all have validated line count
    logging.info('------ Starting dataset level validation: line_cnt_validated ------')
    #db_operation(connection,
    #    '{} UPDATE validation SET line_cnt_validated = CASE {} WHEN (SELECT location FROM data '
    #    'WHERE line_cnt_validated = 0 AND data.location IN (SELECT data_location from manifest_latest_data_files)) '
    #    'IS NOT NULL THEN 0 WHEN validation.integrity_validated = 0 OR (SELECT location FROM data '
    #    'WHERE line_cnt_validated IS NULL AND data.location IN (SELECT data_location from manifest_latest_data_files)) '
    #    'IS NOT NULL THEN 2 ELSE 1 END'.format(
    #        latest_manifest_files_cte, check_corresponding_manifest), [])

    db_operation(connection,
        'UPDATE validation SET line_cnt_validated = CASE {0} WHEN EXISTS (SELECT data.data_key '
        'FROM manifest_tmp, data WHERE {1} AND manifest_tmp.data_key = data.data_key '
        'AND data.line_cnt_validated = 0) THEN 0 WHEN validation.integrity_validated = 0 '
        'OR EXISTS (SELECT data.data_key from manifest_tmp, data WHERE {1} AND manifest_tmp.data_key = data.data_key '
        'AND data.line_cnt_validated IS NULL) THEN 2 ELSE 1 END'.format(check_corresponding_manifest, match_manifest_tmp_with_validation), [])

    # For each group, aggregate over its data files and sum up their line counts
    logging.info('------ Starting dataset level validation: total_line_cnt ------')
    #db_operation(connection,
    #    '{} UPDATE validation SET total_line_cnt = CASE {} ELSE (SELECT SUM(line_cnt) FROM data '
    #    'WHERE data.location IN (SELECT data_location from manifest_latest_data_files)) END'.format(
    #        latest_manifest_files_cte, check_corresponding_manifest), [])
    db_operation(connection,
        'UPDATE validation SET total_line_cnt = CASE {} ELSE (SELECT SUM(data.line_cnt) FROM '
        'data, manifest_tmp WHERE {} AND manifest_tmp.data_key = data.data_key) END'.format(check_corresponding_manifest, match_manifest_tmp_with_validation), [])

    # For each data file, find its corresponding (latest) checksum file - set that to be its
    # checksum_against, compare the checksum of each data file as recorded in the data table
    # against the checksum value of checksum_against as recorded in the checksum table - set
    # that to be its checksum_validated
    logging.info('------ Starting file level validation: checksum_against, checksum_validated ------')
    db_operation(connection,
        'UPDATE data SET checksum_against = (SELECT location FROM checksum '
        'WHERE data.data_key = checksum.data_key ORDER BY checksum.xfer_time DESC LIMIT 1)', [])

    db_operation(connection,
        'UPDATE data SET checksum_validated = CASE '
        'WHEN (SELECT checksum.value FROM checksum WHERE checksum.location = data.checksum_against '
        'AND checksum.data_key = data.data_key) IS NULL OR data.checksum_value IS NULL '
        'THEN NULL WHEN (SELECT checksum.value FROM checksum WHERE checksum.location = '
        'data.checksum_against AND checksum.data_key = data.data_key) = data.checksum_value '
        'THEN 1 ELSE 0 END', [])

    ## EARLY RETURN AFTER DATA FILE LEVEL VALIDATION
    #return

    # For each group, aggregate over its data files and check that all have validated checksum
    logging.info('------ Starting dataset level validation: checksum_validated ------')
    #db_operation(connection,
    #    '{} UPDATE validation SET checksum_validated = CASE {} WHEN (SELECT location FROM data '
    #    'WHERE checksum_validated = 0 AND data.location IN (SELECT data_location from manifest_latest_data_files)) '
    #    'IS NOT NULL THEN 0 WHEN validation.integrity_validated = 0 OR (SELECT location FROM data '
    #    'WHERE checksum_validated IS NULL AND data.location IN (SELECT data_location from manifest_latest_data_files)) '
    #    'IS NOT NULL THEN NULL ELSE 1 END'.format(
    #        latest_manifest_files_cte, check_corresponding_manifest), [])

    db_operation(connection,
        'UPDATE validation SET checksum_validated = CASE {0} WHEN EXISTS (SELECT data.data_key '
        'FROM manifest_tmp, data WHERE {1} AND manifest_tmp.data_key = data.data_key '
        'AND data.checksum_validated = 0) THEN 0 WHEN validation.integrity_validated = 0 '
        'OR EXISTS (SELECT data.data_key from manifest_tmp, data WHERE {1} AND manifest_tmp.data_key = data.data_key '
        'AND data.checksum_validated IS NULL) THEN NULL ELSE 1 END'.format(check_corresponding_manifest, match_manifest_tmp_with_validation), [])

    # Total line count validation

    logging.info('------ Starting dataset level validation: total_line_cnt ------')

    # Use the bootstrap total line count specified in the config file as the total line count of the
    # oldest valid snapshot when available
    logging.info(f'Creating temporary table with bootstrap total_line_cnt information as specified in the config')
    db_operation(connection, 'DROP TABLE IF EXISTS total_line_cnt_tmp', [])
    db_operation(connection,
        'CREATE TABLE total_line_cnt_tmp AS SELECT data_source, data_set, snapshot_dt, '
        'total_line_cnt, integrity_validated, total_line_count_validated, line_cnt_validated, '
        'checksum_validated, snapshot, manifest_location, receiving_time FROM validation', [])
    for datasource, dataset_dict in args['total_line_count_bootstrap_dict'].items():
        for dataset, bootstrap_list in dataset_dict.items():
            for bootstrap_dict in bootstrap_list:
                if 'date' in bootstrap_dict:
                    year, month, day = map(int, bootstrap_dict['date'].split('-'))
                else:
                    year, month, day = 1970, 1, 1
                bootstrap_date = date(year=year, month=month, day=day)
                db_operation(connection,
                    'INSERT INTO total_line_cnt_tmp (data_source, data_set, snapshot_dt, '
                    'total_line_cnt, integrity_validated, total_line_count_validated, '
                    'line_cnt_validated, checksum_validated, snapshot) '
                    'VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)',
                    [datasource, dataset, bootstrap_date, bootstrap_dict['lc'],
                     1, 1, 1, 1, 'bootstrap']
                )

    # For all dataset snapshots, compare total line count against that of the previous valid snapshot
    logging.info('Validating total_line_cnt of all dataset snapshots against their previous '
                 'valid snapshot...')
    db_operation(connection,
        'UPDATE total_line_cnt_tmp AS validation SET total_line_count_validated = CASE {} ELSE '
        'CAST((SELECT (100.0 * ABS(total_line_cnt - prev_total_line_cnt) / prev_total_line_cnt) < %s '
        'FROM (SELECT total_line_cnt AS prev_total_line_cnt FROM total_line_cnt_tmp v WHERE '
        'validation.data_set = v.data_set AND validation.data_source = v.data_source AND '
        'v.snapshot_dt < validation.snapshot_dt AND (v.line_cnt_validated = 1 AND v.checksum_validated = 1 '
        'AND v.integrity_validated = 1 AND (v.total_line_count_validated = 1 OR v.total_line_count_validated '
        'IS NULL)) ORDER BY snapshot_dt DESC LIMIT 1) AS t) AS INTEGER) END'.format(
            check_corresponding_manifest), [args['total_line_cnt_change_thresh']])
    db_operation(connection,
        'UPDATE validation AS v SET total_line_count_validated = (SELECT total_line_count_validated '
        'FROM total_line_cnt_tmp t WHERE v.data_source = t.data_source AND v.data_set = t.data_set '
        'AND v.snapshot = t.snapshot AND v.snapshot_dt = t.snapshot_dt)', [])

    # Datasource level validation

    logging.info('Create a temporary table containing per-datasource dataset list as specified in config file')
    db_operation(connection,
        'CREATE TEMPORARY TABLE dataset_tmp (data_source TEXT, data_set TEXT, snapshot TEXT, '
        'snapshot_dt DATE, UNIQUE(data_source, data_set, snapshot, snapshot_dt))', [])
    res = db_operation(connection,
        'SELECT DISTINCT data_source, snapshot, snapshot_dt FROM validation', [])
    for datasource, snapshot, snapshot_dt in res:
        datasets = args['dataset_dict'][datasource] if datasource in args['dataset_dict'] else []
        for dataset in datasets:
            db_operation(connection,
                'INSERT INTO dataset_tmp (data_source, data_set, snapshot, snapshot_dt) '
                'VALUES (%s, %s, %s, %s)',
                [datasource, dataset, snapshot, snapshot_dt])

    logging.info('Create a temporary validation table with only the datasets explicitly specified in the dataset list config file')
    # collect column names from validation table
    validation_key_columns = ['data_source', 'data_set', 'snapshot', 'snapshot_dt']
    validation_all_columns = db_operation(connection, "select column_name from information_schema.columns where table_name = 'validation'", [])
    validation_select_columns = ['v.'+item[0] for item in validation_all_columns if item[0] not in validation_key_columns]
    db_operation(connection,
        'CREATE TEMPORARY TABLE validation_tmp AS SELECT dt.*, {} FROM dataset_tmp dt LEFT JOIN validation '
        'v ON dt.data_source = v.data_source AND dt.data_set = v.data_set AND dt.snapshot = '
        'v.snapshot AND dt.snapshot_dt = v.snapshot_dt'.format(
        ', '.join(validation_select_columns)), [])

    count = db_operation(connection, 'SELECT COUNT(*) FROM dataset_tmp', [])
    print(count)
    count_1 = db_operation(connection, 'SELECT COUNT(*) FROM validation_tmp', [])
    print(count_1)

    logging.info('Calculate per-datasource validation results')
    db_operation(connection,
        'INSERT INTO validation_data_source (data_source, snapshot, snapshot_dt) '
        'SELECT DISTINCT data_source, snapshot, snapshot_dt FROM validation_tmp '
        'WHERE data_source != data_set '
        'ON CONFLICT (data_source, snapshot, snapshot_dt) DO NOTHING', [])

    cols = ('total_line_count_validated', 'line_cnt_validated', 'checksum_validated',
            'integrity_validated')
    subquery = (
        '{0} = CASE WHEN (SELECT data_set FROM validation_tmp v '
        'WHERE v.{0} = 0 AND v.data_source = vd.data_source AND v.snapshot = vd.snapshot '
        'ORDER BY snapshot_dt DESC LIMIT 1) '
        'IS NOT NULL THEN 0 WHEN (SELECT data_set FROM validation_tmp v '
        'WHERE v.{0} IS NULL AND v.data_source = vd.data_source AND v.snapshot = vd.snapshot '
        'ORDER BY snapshot_dt DESC LIMIT 1) '
        'IS NOT NULL THEN NULL ELSE 1 END')

    db_operation(connection,
        'UPDATE validation_data_source AS vd SET '
        'receiving_time = (SELECT MAX(receiving_time) FROM validation_tmp v WHERE v.data_source = '
        'vd.data_source AND v.snapshot = vd.snapshot), '
        'total_line_cnt = (SELECT SUM(total_line_cnt) FROM validation_tmp v WHERE v.data_source = '
        'vd.data_source AND v.snapshot = vd.snapshot), '
            + ', '.join([subquery.format(c) for c in cols]), [])


def run_validation(db_conn, config_package, run_on_airflow_flag=True, dry_run_flag=False):

    ensure_single_instance(SIGNATURE)

    args = config_package['validation_config']
    args['interval_dict'] = config_package['interval_dict']
    args['dataset_dict'] = config_package['dataset_dict']
    args['total_line_count_bootstrap_dict'] = config_package['total_line_count_bootstrap_dict']
    args['fraudnet_alert_list'] = config_package['fraudnet_alert_list']
    args['ms_teams_channel_url'] = config_package['ms_teams_channel_url']
    args['https_proxy_url'] = config_package['https_proxy_url']
    args['http_proxy_url'] = config_package['http_proxy_url']

    create_local_directory(args['log_dir'])
    if not run_on_airflow_flag:
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
    start = time.time()
    logging.basicConfig(filename=f'{args["log_dir"]}/validation.log.'
                                 f'{datetime.fromtimestamp(start).strftime("%Y%m%d")}',
                        level=logging.DEBUG, filemode='w',
                        format='%(asctime)s %(name)s - %(levelname)s - %(message)s',
                        datefmt='%Y-%m-%d %H:%M:%S')
    config_logging_level(logging)
    logging.info('====== Start validation ======')

    logging.info('Initiating validation...')
    validate_inventory(db_conn, args)

    #logging.info('Initiating alerting...')
    process_alerting(db_conn, args)

    elapsed = time.time() - start
    logging.info(f'[RUN TIME STATS]: Running validation.py against database '
                 f'took {elapsed} seconds')


if __name__ == '__main__':

    print('====== start ======')
    parser = argparse.ArgumentParser(description='Execute validation against the inventory database based on the input config file.')
    parser.add_argument('--config', dest='config_file_path', required=True,
                        help='Path to the YAML file specifying the validation configuration.')
    parser.add_argument('--dry-run', dest='dry_run_flag', action='store_true',
                        help='Boolean value indicating if this is a dry run. Dry runs '
                             'will generate reports and logs but not actually fire alerts.')

    args = parser.parse_args()

    ## DB and S3 connection below works on EC2 but not on Airflow
    postgres_db_config = {'hostname': 'datavalidation-zsddkcgtk35qfxw9.c9qafslrwnv1.us-east-1.rds.amazonaws.com', 'login':'postgres', 'password':'8SdnH28TDeEx6n2NSQVALIDwCVxq2osFaT4DIc762hyKVF8glqk2KfQRRMQilnDK'}
    db_conn = create_connection(postgres_db_config['hostname'], postgres_db_config['login'], postgres_db_config['password'])

    print('--- collect config files ---')
    from utilities import parse_yaml_config_file
    config_package = {}
    main_config = parse_yaml_config_file(args.config_file_path)
    config_package['validation_config'] = main_config
    config_package['interval_dict'] = parse_yaml_config_file('configs/transfer_interval.yaml')
    config_package['dataset_dict'] = parse_yaml_config_file('configs/dataset_list.yaml')
    config_package['total_line_count_bootstrap_dict'] = parse_yaml_config_file('configs/total_line_count_bootstrap.yaml')
    config_package['fraudnet_alert_list'] = parse_yaml_config_file('configs/fraudnet_alert_list.yaml')
    config_package['ms_teams_channel_url'] = 'https://experian.webhook.office.com/webhookb2/4c18cc76-119c-4521-abe0-ef939bd1e583@be67623c-1932-42a6-9d24-6c359fe5ea71/IncomingWebhook/61d4794e7cfd4c83b2da230c1988b799/178ebd7c-3a5f-4b4b-9ce5-ab63bfd1947b'
    config_package['https_proxy_url'] = 'http://10.248.147.6:9090'
    config_package['http_proxy_url'] = 'http://10.248.147.6:9090'

    run_validation(db_conn, config_package, dry_run_flag=args.dry_run_flag)
