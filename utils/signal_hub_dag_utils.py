import json
from airflow.utils.task_group import TaskGroup
from signalhub.common.operators.emr import SHEmrCreateJobFlowOperator
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.sensors.emr_step import EmrStepSensor
from airflow.providers.amazon.aws.hooks.base_aws import AwsBaseHook
from airflow.operators.python_operator import PythonOperator
from airflow.models import Variable
from datetime import datetime, timedelta
import hashlib


default_spark_submit_args = [
    '--deploy-mode', 'client',
    '--master', 'yarn',
    '--driver-memory', '24g',
    '--executor-memory', '30G',
    '--num-executors', '60',
    '--executor-cores', '4',
    '--conf', 'spark.sql.shuffle.partitions=2000',
    '--conf', 'spark.driver.maxResultSize=4G',
    '--conf', 'spark.pyspark.python=${PYSPARK_PYTHON}',
    '--conf', 'spark.pyspark.driver.python=${PYSPARK_DRIVER_PYTHON}',
]
default_bootstrap_steps = [
    'export AWS_DEFAULT_REGION=us-east-1',
    'source ${MINICONDA_PATH}/etc/profile.d/conda.sh',
    'conda activate ${OEIDP_VENV}',
    'source /tmp/env_var_setup.sh',
]


def generate_oeidp_parsing_emr_steps(inquiry_filename, input_path, output_path_processed, parser_config, snapshot_date):
    return [
        {
            'Name': f'parsing_{inquiry_filename}',
            'ActionOnFailure': 'CONTINUE',
            'HadoopJarStep': {
                'Jar': 's3://us-east-1.elasticmapreduce/libs/script-runner/script-runner.jar',
                'Args': [
                    '/home/<USER>/inquiry_data_parser.sh',
                    '--service', 'airflow',
                    '--base_path', 's3://ascend-oeidp-prod-data-074628058490-us-east-1',
                    '--database', 'oeidp',
                    '--batch_no', 'null',
                    '--input_path', input_path,
                    '--snapshot_date', snapshot_date,
                    '--parser_config', parser_config,
                    '--output_path_processed', output_path_processed,
                    '--inquiry_filename', inquiry_filename,
                    '--mode', 'inquiry'
                ],
            },
        }
    ]


def get_oeidp_config(base_path, database, batch_no, oeidp_config):
    return oeidp_config if oeidp_config is not None else f'{base_path}/{database}/v{batch_no}/datasource_config.yaml'


def generate_oeidp_pinning_emr_steps(
        inquiry_filename, output_path_processed, output_path_result, base_path, batch_no,
        database='oeidp', ds_exclusion_group='all', oeidp_config=None):
    oeidp_config = get_oeidp_config(base_path, database, batch_no, oeidp_config)
    return [
        {
            'Name': f'pinning_{inquiry_filename}',
            'ActionOnFailure': 'CONTINUE',
            'HadoopJarStep': {
                'Jar': 's3://us-east-1.elasticmapreduce/libs/script-runner/script-runner.jar',
                'Args': [
                    '/home/<USER>/inquiry_data_pinning.sh',
                    '--service', 'airflow',
                    '--base_path', base_path,
                    '--database', database,
                    '--batch_no', batch_no,
                    '--dataset', 'us',
                    '--scheduler', 'standalone',
                    '--config', oeidp_config,
                    '--ds_exclusion_group', ds_exclusion_group,
                    '--application', 'inquiry',
                    '--inquiry_filename', inquiry_filename,
                    '--output_path_processed', output_path_processed,
                    '--output_path_result', output_path_result,
                ],
            },
        }
    ]


def generate_oeidp_identity_and_element_lookup_emr_steps(
        snapshot_month, cluster_version, base_path, batch_no,
        output_path, pin_mapping_path, database='oeidp', ds_exclusion_group='all', oeidp_config=None):
    oeidp_config = get_oeidp_config(base_path, database, batch_no, oeidp_config)
    return [
        {
        'Name': 'generate_oeidp_linkage_tables',
        'ActionOnFailure': 'CONTINUE',
        'HadoopJarStep': {
            'Jar': 'command-runner.jar',
            'Args': [
                "bash", '-c',
                ';'.join([
                    *default_bootstrap_steps,
                    ' '.join([
                        'spark-submit',
                        '--deploy-mode',  'client',
                        '--master', 'yarn',
                        '--driver-memory', '24g',
                        '--executor-memory', '30G',
                        '--num-executors', '60',
                        '--executor-cores', '4',
                        '--conf', 'spark.sql.shuffle.partitions=5000',
                        '--conf', 'spark.driver.maxResultSize=4G',
                        '--conf', 'spark.pyspark.python=${PYSPARK_PYTHON}',
                        '--conf', 'spark.pyspark.driver.python=${PYSPARK_DRIVER_PYTHON}',
                        '/tmp/oeidp/signal_hub/src/linkage_tables'
                        '/sh_generate_oeidp_identity_and_element_lookup_tables.py',
                        '--month', snapshot_month,
                        '--cluster_version', cluster_version,
                        '--base_path', base_path,
                        '--database', database,
                        '--batch_no', batch_no,
                        '--ds_exclusion_group', ds_exclusion_group,
                        '--config', oeidp_config,
                        '--pin_mapping_table', pin_mapping_path,
                        '--output_path', output_path
                        ])
                    ])
                ],
            }
        }
    ]


def generate_identity_and_element_lookup_emr_steps(
        datasource_name, product, snapshot_month, cluster_version, base_path, batch_no,
        ic_content_path, cc_content_path, inquiry_result, output_path, pin_mapping_path,
        database='oeidp', ds_exclusion_group='all', oeidp_config=None):
    oeidp_config = get_oeidp_config(base_path, database, batch_no, oeidp_config)
    return [
        {
            'Name': f'identity_and_element_lookup_of_{datasource_name}',
            'ActionOnFailure': 'CONTINUE',
            'HadoopJarStep': {
                'Jar': 'command-runner.jar',
                'Args': [
                    'bash', '-c',
                    ';'.join([
                        *default_bootstrap_steps,
                        ' '.join([
                            'spark-submit',
                            '--deploy-mode', 'client',
                            '--master', 'yarn',
                            '--driver-memory', '24g',
                            '--num-executors', '60',
                            '--executor-cores', '4',
                            '--executor-memory', '30g',
                            '--conf', 'spark.sql.shuffle.partitions=4000',
                            '--conf', 'spark.driver.maxResultSize=2G',
                            '--conf', 'spark.pyspark.python=${PYSPARK_PYTHON}',
                            '--conf', 'spark.pyspark.driver.python=${PYSPARK_DRIVER_PYTHON}',
                            '/tmp/oeidp/signal_hub/src/linkage_tables/sh_generate_identity_and_element_lookup_tables.py',
                            '--inquiry_month', snapshot_month,
                            '--cluster_version', cluster_version,
                            '--base_path', base_path,
                            '--database', database,
                            '--batch_no', batch_no,
                            '--ds_exclusion_group', ds_exclusion_group,
                            '--config', oeidp_config,
                            '--pin_mapping_table', pin_mapping_path,
                            '--content_path', ic_content_path,
                            '--cc_content_path', cc_content_path,
                            '--inquiry_result', inquiry_result,
                            '--output_path', output_path,
                            '--product', product
                        ])
                    ])
                ]
            }
        }
    ]


def generate_linkage_stats_emr_steps(linkage_table_path, month_start, month_end, product, timestamp):
    return [
        {
            'Name': f'generate_linkage_table_stats',
            'ActionOnFailure': 'CONTINUE',
            'HadoopJarStep': {
                'Jar': 'command-runner.jar',
                'Args': [
                    "bash", '-c',
                    ';'.join([
                        *default_bootstrap_steps,
                        ' '.join([
                            'spark-submit',
                            '--deploy-mode',  'client',
                            '--master', 'yarn',
                            '--driver-memory', '24g',
                            '--executor-memory', '30G',
                            '--num-executors', '60',
                            '--executor-cores', '4',
                            '--conf', 'spark.sql.shuffle.partitions=4000',
                            '--conf', 'spark.driver.maxResultSize=6G',
                            '--conf', 'spark.pyspark.python=${PYSPARK_PYTHON}',
                            '--conf', 'spark.pyspark.driver.python=${PYSPARK_DRIVER_PYTHON}',
                            '/tmp/oeidp/signal_hub/src/linkage_tables'
                            '/sh_linkage_table_stats.py',
                            '--linkage_path', linkage_table_path,
                            '--month_start', month_start,
                            '--month_end', month_end,
                            '--product', product,
                            '--timestamp', timestamp
                        ])
                    ])
                ],
            },
        }
    ]


def generate_fraudnet_feedback_preprocessing_emr_steps(input_path, output_path):
    return [
        {
            'Name': f'preprocess_fraudnet_feedback',
            'ActionOnFailure': 'CONTINUE',
            'HadoopJarStep': {
                'Jar': 'command-runner.jar',
                'Args': [
                    "bash", '-c',
                    ';'.join([
                        *default_bootstrap_steps,
                        ' '.join([
                            'spark-submit',
                            '--deploy-mode',  'client',
                            '--master', 'yarn',
                            '--driver-memory', '24g',
                            '--executor-memory', '30G',
                            '--num-executors', '60',
                            '--executor-cores', '4',
                            '--conf', 'spark.sql.shuffle.partitions=2000',
                            '--conf', 'spark.driver.maxResultSize=4G',
                            '--conf', 'spark.pyspark.python=${PYSPARK_PYTHON}',
                            '--conf', 'spark.pyspark.driver.python=${PYSPARK_DRIVER_PYTHON}',
                            '/tmp/oeidp/signal_hub/src/linkage_tables/preprocess_fraudnet_feedback.py',
                            '--input_path', input_path,
                            '--output_path', output_path,
                        ])
                    ])
                ],
            },
        }
    ]


def generate_sure_profile_tag_and_score_emr_steps(last_update_date, trade_table_path, tag_table_save_path, score_table_save_path, tag_version='cc_pl'):
    return [
        {
            'Name': f'generate_sure_profile_tag_and_score',
            'ActionOnFailure': 'CONTINUE',
            'HadoopJarStep': {
                'Jar': 'command-runner.jar',
                'Args': [
                    "bash", '-c',
                    ';'.join([
                        *default_bootstrap_steps,
                        ' '.join([
                            'spark-submit',
                            '--deploy-mode',  'client',
                            '--master', 'yarn',
                            '--driver-memory', '24g',
                            '--executor-memory', '30G',
                            '--num-executors', '60',
                            '--executor-cores', '4',
                            '--conf', 'spark.sql.shuffle.partitions=2000',
                            '--conf', 'spark.driver.maxResultSize=4G',
                            '--conf', 'spark.pyspark.python=${PYSPARK_PYTHON}',
                            '--conf', 'spark.pyspark.driver.python=${PYSPARK_DRIVER_PYTHON}',
                            '/tmp/oeidp/signal_hub/src/linkage_tables/generate_sure_profile_tag_and_score.py',
                            '--last_update_date', last_update_date,
                            '--tag_version', tag_version,
                            '--trade_table_path', trade_table_path,
                            '--tag_table_save_path', tag_table_save_path,
                            '--score_table_save_path', score_table_save_path,
                        ])
                    ])
                ],
            },
        }
    ]


def hash_linkage_table_emr_steps(linkage_table_path, month, product, hashed_linkage_path, secret_name):
    return [
        {
            'Name': f'hash_linkage_table',
            'ActionOnFailure': 'CONTINUE',
            'HadoopJarStep': {
                'Jar': 'command-runner.jar',
                'Args': [
                    "bash", '-c',
                    ';'.join([
                        *default_bootstrap_steps,
                        ' '.join([
                            'spark-submit',
                            '--deploy-mode',  'client',
                            '--master', 'yarn',
                            '--driver-memory', '24g',
                            '--executor-memory', '30G',
                            '--num-executors', '60',
                            '--executor-cores', '4',
                            '--conf', 'spark.sql.shuffle.partitions=500',
                            '--conf', 'spark.driver.maxResultSize=4G',
                            '--conf', 'spark.pyspark.python=${PYSPARK_PYTHON}',
                            '--conf', 'spark.pyspark.driver.python=${PYSPARK_DRIVER_PYTHON}',
                            '/tmp/oeidp/signal_hub/src/linkage_tables/hash_linkage_table.py',
                            '--linkage_path', linkage_table_path,
                            '--month', month,
                            '--product', product,
                            '--hashed_linkage_path', hashed_linkage_path,
                            '--secret_name', secret_name
                        ])
                    ])
                ],
            },
        }
    ]


def generate_pyspark_emr_steps(
        step_name, pyspark_script_args, spark_submit_args=default_spark_submit_args, bootstrap_steps=default_bootstrap_steps):
    return [
        {
            'Name': step_name,
            'ActionOnFailure': 'CONTINUE',
            'HadoopJarStep': {
                'Jar': 'command-runner.jar',
                'Args': [
                    "bash", '-c',
                    ';'.join([
                        *bootstrap_steps, ' '.join(['spark-submit', *spark_submit_args, *pyspark_script_args])
                    ])
                ],
            },
        }
    ]


def create_emr_task_group(group_id, dag, emr_conn_id, aws_conn_id, last_job_flow_id, emr_steps, emr_ami_variable=None, job_flow_overrides=None):
    flow_overrides = {"Name": f"{dag.dag_id}_dag"}
    if not job_flow_overrides:
        job_flow_overrides = {}
    flow_overrides.update(job_flow_overrides)
    task_group = TaskGroup(group_id=group_id, dag=dag)
    with task_group:
        if emr_ami_variable:
            read_secret = PythonOperator(
                task_id='set_custom_ami',
                python_callable=add_custom_ami_to_emr_overrides,
                op_kwargs=dict(
                    airflow_variable_ami_secret=emr_ami_variable,
                    flow_overrides=flow_overrides,
                    aws_conn_id=aws_conn_id)
            )
            flow_overrides = f"{{{{ task_instance.xcom_pull(task_ids='{group_id}.set_custom_ami', key='return_value') }}}}"

        cluster_creator = SHEmrCreateJobFlowOperator(
            task_id='create_job_flow',
            emr_conn_id=emr_conn_id,
            aws_conn_id=aws_conn_id,
            job_flow_id=last_job_flow_id,
            job_flow_overrides=flow_overrides,
        )
        cluster_id = f"{{{{ task_instance.xcom_pull(task_ids='{group_id}.create_job_flow', key='return_value') }}}}"
        step_adder = EmrAddStepsOperator(
            task_id='add_steps',
            aws_conn_id=aws_conn_id,
            job_flow_id=cluster_id,
            steps=emr_steps,
        )
        step_id = f"{{{{ task_instance.xcom_pull(task_ids='{group_id}.add_steps', key='return_value')[-1] }}}}"
        step_checker = EmrStepSensor(
            task_id=f'watch_step',
            aws_conn_id=aws_conn_id,
            job_flow_id=cluster_id,
            step_id=step_id,
        )

        if emr_ami_variable:
            read_secret >> cluster_creator >> step_adder >> step_checker
        else:
            cluster_creator >> step_adder >> step_checker
    return task_group, cluster_id


def get_datasource_hash(datasource):
    datasource_spec = datasource[datasource.rfind('_') + 1:]
    return hashlib.sha256(datasource_spec.encode()).hexdigest()[0:10]


def get_snapshot_date(execution_date):
    _execution_date = datetime.strptime(execution_date, '%Y-%m-%d')
    snapshot_date = _execution_date.replace(day=1) - timedelta(days=1)
    return snapshot_date.strftime('%Y%m%d')

def add_custom_ami_to_emr_overrides(**kwargs):
    emr_ami = Variable.get(key=kwargs.get("airflow_variable_ami_secret"), deserialize_json=True)
    secret_id = emr_ami["secret_id"]
    secret_key = emr_ami["secret_key"]
    flow_overrides = kwargs.get("flow_overrides")
    aws_conn_id = kwargs.get("aws_conn_id")
    secret_str = read_from_aws_secret(secret_id, aws_conn_id)
    custom_ami = json.loads(secret_str).get(secret_key)
    flow_overrides["CustomAmiId"] = custom_ami
    return flow_overrides

def read_from_aws_secret(secret_id, aws_conn_id):
    hook = AwsBaseHook(client_type='secretsmanager', aws_conn_id=aws_conn_id)
    client = hook.get_client_type('secretsmanager')
    response = client.get_secret_value(SecretId=secret_id)
    return response["SecretString"]
