from typing import Any, Dict, List, Optional
import ast
from signalhub.common.utils import aws
from airflow.exceptions import AirflowException
from airflow.providers.amazon.aws.hooks.emr import EmrHook
from airflow.providers.amazon.aws.operators.emr_create_job_flow import EmrCreate<PERSON><PERSON><PERSON><PERSON>Operator, EmrClusterLink


class SHEmrCreateJobFlowOperator(EmrCreateJobFlowOperator):
    template_fields = ['job_flow_id', 'job_flow_overrides']
    template_ext = ('.json',)
    template_fields_renderers = {"job_flow_overrides": "json"}
    ui_color = '#f9c915'
    operator_extra_links = (EmrClusterLink(),)
    _running_states = ["STARTING", "BOOTSTRAPPING", "RUNNING", "WAITING"]

    def __init__(self, *,
                 job_flow_id: Optional[str] = None,
                 **kwargs):
        self.job_flow_id = job_flow_id
        super().__init__(**kwargs)

    def execute(self, context: Dict[str, Any]) -> str:
        if not self.emr_conn_id:
            raise AirflowException('emr_conn_id must be present to use create_job_flow')
        if not self.aws_conn_id:
            raise AirflowException('aws_conn_id must be present to use create_job_flow')

        if self.job_flow_id:
            emr_hook = EmrHook(aws_conn_id=self.aws_conn_id, region_name=self.region_name)

            emr = emr_hook.get_conn()

            response = emr.describe_cluster(ClusterId=self.job_flow_id)
            state = response.get("Cluster", {}).get("Status", {}).get("State")
            if state and state.upper() in SHEmrCreateJobFlowOperator._running_states:
                self.log.info('Reusing "%s" EMR cluster %s', state, self.job_flow_id)
                return self.job_flow_id

        if isinstance(self.job_flow_overrides, str):
            job_flow_overrides: Dict[str, Any] = ast.literal_eval(self.job_flow_overrides)
            self.job_flow_overrides = job_flow_overrides
        else:
            job_flow_overrides = self.job_flow_overrides

        # merge overrides with emr_conn_id
        if job_flow_overrides:
            self.job_flow_overrides = aws.merge_flow_overrides(self.emr_conn_id, job_flow_overrides)

        return super().execute(context)