from typing import Any, Dict, List, Optional
import logging

from airflow.providers.amazon.aws.hooks.emr import EmrHook
from airflow.exceptions import AirflowException

log = logging.getLogger(__name__)


def _merge(left, right):
    # type: (Any, Any) -> Any
    if type(left) != type(right):
        raise ValueError(f"Data type of provided arguments ({type(left)} != {type(right)}) do not match.")

    # what if right is None or something else
    if isinstance(left, dict) and isinstance(right, dict):
        merged = {}
        for k, v in left.items():
            if v is not None:
                # when key exists in both dictionaries
                if k in right:
                    if right.get(k) is not None:
                        merged[k] = _merge(v, right.get(k))
                else:
                    # add keys that are only found in left dict
                    merged[k] = v
        # add keys that are only found in right dict
        for k,v in right.items():
            if k not in left and v is not None:
                merged[k] = v

        return merged

    return right


def merge_flow_overrides(emr_conn_id, job_flow_overrides):
    # type: (str, Dict[str, Any]) -> Dict[str, Any]
    if not emr_conn_id:
        raise AirflowException('emr_conn_id must be present to use create_job_flow')

    emr = EmrHook(emr_conn_id=emr_conn_id)
    emr_conn = emr.get_connection(emr_conn_id)

    config = emr_conn.extra_dejson.copy()
    return _merge(config, job_flow_overrides)

def get_emr_instance_group(role, instance_count, instance_type, volume=None, volume_size=None):
    if role != "CORE" and role != "MASTER" and role != "TASK":
        raise AirflowException("Only MASTER and CORE roles are acceptable.")

    volume = {
       "EbsBlockDeviceConfigs":[
          {
             "VolumeSpecification":{
                "SizeInGB": volume_size,
                "VolumeType": volume
             },
             "VolumesPerInstance": 1
          }
       ],
       "EbsOptimized": True
    } if volume else None

    return {
        "InstanceCount": instance_count,
        "InstanceType": instance_type,
        "Market": "ON_DEMAND",
        "Name": role,
        "InstanceRole": role,
        "EbsConfiguration": volume
     }

def get_emr_master_group(instance_count, instance_type, volume="gp2", volume_size=10):
    return get_emr_instance_group(role="MASTER",
                                  instance_count=instance_count,
                                  instance_type=instance_type,
                                  volume=volume,
                                  volume_size=volume_size)

def get_emr_core_group(instance_count, instance_type, volume=None, volume_size=None):
    return get_emr_instance_group(role="CORE",
                                  instance_count=instance_count,
                                  instance_type=instance_type,
                                  volume=volume,
                                  volume_size=volume_size)