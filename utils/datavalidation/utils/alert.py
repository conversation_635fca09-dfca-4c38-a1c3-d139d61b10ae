import logging
import os
import requests
from datetime import datetime, timedelta

from signalhub.datavalidation.utils.utilities import db_operation, format_timestamp, send_notification, get_receiving_date_from_filename
#from utilities import db_operation, format_timestamp, send_notification


MAX_ALERT_DISPLAY_LINES = 3
TS_FORMAT = '%Y-%m-%d %H:%M:%S'

def process_alerting(connection, args, dry_run=False):
    """Processes alerting based on the current validation table.

    Alerts are broken down into 3 levels, each of which has different types and exhibits
    different alerting patterns as follows:

    Data File:

    File level alerts are issued exactly once after a specified grace period from the file's
    receiving time (currently implemented as last modified time). This grace period exists to
    prevent preemptive alerts on recently receivied data files for which corresponding manifest
    files and/or checksum files may not have been tranferred and/or inventoried yet.

        line count: Alerted if the expected line count from the manifest file is missing, the
                    actual line count computed from the data file is missing, or both the expected
                    and actual line counts do not match.

        checksum:   Alerted if the expected checksum from the checksum file is missing, the
                    actual checksum computed from the data file is missing, or both the expected
                    and actual checksums do not match.

    Dataset:

    Dataset level alerts are repeatedly issued within some specified alerting period past the
    deadline. e.g. if this alerting cronjob runs every 6 hours and the alerting period is 1 day,
    a dataset snapshot that fails to validate will cause an alert to be issued 4 times after its
    transfer deadline. The deadline is computed using the dataset's receiving time (currently
    implemented as the latest mtime among the dataset's data files) + configurable transfer interval
    + configurable transfer grace period. For dataset level alerts, the alerts are hierarchical
    such that only the most general alert will be issued, as the more specific alerts are implied
    by the general alert. e.g. a missing file implies total line count validation should also fail,
    so only issue an alert for missing file.

        deadline:           Alerted if the deadline has passed wrt the latest dataset snapshot and
                            the expected snapshot (or more specifically its manifest file) has not
                            arrived yet.

        ambiguous manifest: Alerted if the dataset snapshot has arrived but the correct manifest file
                            for the snapshot cannot be determined. This is possible if the data
                            management script fails to correctly extract datasource/dataset metadata
                            and ends up assigning multiple manifest files from different datasets
                            to the same snapshot. Similarly, this failure can also occur if
                            multiple datasets were erroneously transferred to the same directory
                            and the case is not explicitly special handled by the inventory script.

        missing all:        Alerted if the manifest file for the snapshot has arrived but all data
                            files referenced by the manifest file are missing from the data table.

        missing file:       Alerted if the manifest file for the snapshot has arrived but the
                            snapshot is incomplete as certain files, not all, referenced by the
                            manifest file are missing from the data table.

        total line count:   Alerted if all data files referenced by the manifest file has arrived
                            but the total line count of all the data files of the received snapshot
                            deviates by more than some configurable percentage from the previous
                            valid snapshot ("valid" snapshot meaning all of integrity validated,
                            total line count validated, line count validated, and checksum validated
                            unless the snapshot is the oldest snapshot in which case it does not
                            need to be total line count validated as it does not have a previous
                            valid snapshot itself to compare against).

    Datasource:

    Datasource level alerts are, like dataset level alerts, repeatedly issued within some specified
    alerting period past the deadline. At the datasource level, the deadline is taken to be the
    latest deadline among the datasource's datasets. Something important to note, though not
    necessarily due to the implementation of alerting itself, is that datasource level alerts will
    only be issued for those datasources specifically configured in the dataset list config file.
    Because datasource level validation only carries out validation over the datasources and their
    respective datasets specified in the config file, and as a consequence of alerting being
    dependent on the validation results of the validation script, alerting will also only alert
    based on those datasources and datasets in the config file as well.

        unexpected dataset: Alerted if a dataset not belonging to some datasource, as according to
                            the dataset list config file, is ever encountered. For this alert only,
                            the deadline upon which the alerting period is computed does not take
                            into consideration transfer interval, just the receiving time of the
                            datasource. Note that the datasource level validation, and thereby
                            alerting, relies on the dataset list config file being actively
                            maintained and kept up to date to include all datasets expected per
                            each datasource. If the config file is well maintained, we should expect
                            this particular alert to be very rare.

        deadline:           Alerted if after the datasource deadline, ALL datasets of the datasource
                            is missing. This means that all datasets of this datasource snapshot
                            must've already each issued a deadline alert. This datasource level
                            deadline alert then simply serves as a strong reminder that ALL expected
                            datasets for this datasource have missed their respective deadlines.

    Edge Cases:

        1. Computing deadline without data receiving time:

           For alerting purposes, a deadline must always be computed based on the previous valid
           snapshot's receving time. This receiving time is primarily computed as the latest
           modified time of the dataset's received files. However, it is possible for a manifest
           file for a particular snapshot to be transferred and have all its data files be missing.
           For such a case, the receiving time of the dataset is not known (and thus left NULL
           in the validation table) and instead the modified time of the manifest file itself is used
           to approximate the deadline. Furthermore, it is also possible, though much more unlikely,
           that the snapshot does not have a manifest file at all. In this particular case, we
           cannot possibly determine the deadline for the snapshot, and we simply forgo alerting
           altogether. This may cause alerts to not fire as in the case where there are multiple
           consecutive bad snapshots for which deadlines cannot be computed so only one alert
           will be fired instead of however many bad snapshots were received.

        2. First dataset cold start:

           With the way this code is currently implemented, validation failures that occur as
           the first snapshot of a dataset will not trigger an alert. This is fundamentally because
           there is no previous snapshot for which a deadline can be determined from. If for some
           reason issuing alerts for snapshots that are the first dataset of their kind becomes
           necessary, some deadline bootstrapping feature must be added.

        3. Unspecified transfer interval:

           As previously described, the deadline for each dataset deadline is computed as
           receiving time + transfer interval + transfer grace period. For each dataset or
           datasource, the transfer interval is specified in the transfer interval config file.
           If a dataset/datasource is encountered for which a transfer interval must be retrieved
           but is not available from the config file, a configurable default interval will be
           used instead to compute the deadline.
    """

    def _send_alert(alert_msg):
        if not dry_run:
            status_code = send_notification(alert_msg, args['ms_teams_channel_url'], https_proxy_url=args['https_proxy_url'], http_proxy_url=args['http_proxy_url'])
            if status_code != requests.codes.ok:
                return False
        else:
            logging.info('Alert successfully sent (dry_run).')
            print(alert_msg)

        return True

    def _suppress_alert(deadline, datasource, data_filename=None, checksum_validation_passed=None, line_count_against=None):
        # TODO: Temporarily suppress cem_phonepin and cem_coa related alerts
        if datasource.lower().startswith('cem_phonepin') or datasource.lower().startswith('cem_coa'):
            return True

        # Skip placeiq data if it passed checksum validation and do not have manifest file
        # TODO: Apply this logic to other online data, credit header or deceased data which does not have manifest from vendor
        if datasource.lower().startswith('placeiq') and checksum_validation_passed == 1 and line_count_against is None:
            return True


        # Suppress line count mismatch alerts for fraudnet
        if data_filename and data_filename.startswith('fraudnet') and checksum_validation_passed == 1:
            return True

        # Suppress line count mismatch alerts for ems. Manifest files for ems do not contain line counts
        if data_filename and data_filename.startswith('ems') and checksum_validation_passed == 1:
            return True

        # By default suppress all fraudnet related alerts except for ones corresponding to the
        # datasets that are explicitly defined in fraudnet_alert_list
        if datasource.lower().startswith('fraudnet') and \
           datasource not in args['fraudnet_alert_list']:
            return True

        return datetime.now() - deadline > timedelta(days=args['alerting_period'])

    def _generate_missing_value_alert_msg(value_type, v1_from, v2_from, v1_type, v2_type, v1, v2):
        alert_msg = ''
        if not v1 and not v2:
            alert_msg += f'Missing both {v1_type} and {v2_type} {value_type} values'
        elif not v1:
            alert_msg += f'Missing {v1_type} {value_type} from {v1_from}, ' \
                         f'{v2_type.capitalize()}: {v2}'
        elif not v2:
            alert_msg += f'Missing {v2_type} {value_type} from {v2_from}, ' \
                         f'{v1_type.capitalize()}: {v1}'
        return alert_msg

    def _normalize_receiving_time(receiving_time, manifest_location):
        if isinstance(receiving_time, datetime):
            return receiving_time
        receiving_time = format_timestamp(receiving_time, ts_format=TS_FORMAT)
        if not receiving_time and manifest_location:
            manifest_xfer_time, manifest_mtime = db_operation(connection,
                'SELECT xfer_time, last_modified_time FROM manifest_file WHERE location = %s',
                [manifest_location])[0]
            receiving_time = manifest_xfer_time or manifest_mtime
        return receiving_time

    def _is_after_transfer_deadline(datasource, dataset, receiving_time):
        assert receiving_time is not None and isinstance(receiving_time, datetime), \
               f'malformated receiving_time ({receiving_time}, {type(receiving_time)})'

        # Get the transfer interval to compute snapshot deadline
        interval = None
        if dataset in args['interval_dict']['dataset_level'] \
            and args['interval_dict']['dataset_level'][dataset] is not None:
            interval = timedelta(days=args['interval_dict']['dataset_level'][dataset])
        elif datasource in args['interval_dict']['datasource_level'] \
            and args['interval_dict']['datasource_level'][datasource] is not None:
            interval = timedelta(days=args['interval_dict']['datasource_level'][datasource])
        if not interval:
            interval = timedelta(days=args['default_interval'])
            logging.warning(f'Failed to retrieve the transfer interval for ({datasource}, {dataset}, '
                            f'{snapshot}), using default interval of {interval}...')

        grace_period = timedelta(days=args['transfer_grace_period'])
        deadline = receiving_time + interval + grace_period

        return datetime.now() - deadline > timedelta(0), deadline

    _send_alert('Display alerts below...')

    logging.info('========== DATA FILE VALIDATION ALERTS ==========')

    # Process file level validation alerts (line count, checksum)
    # TODO: Consider using the same deadline as transfer deadline rather than use a new data file
    #       grace period. If we intend to issue alerts for only data files referenced by some
    #       manifest file, this change would be easy. However, if we want to alert for all data
    #       files, even those not reachable by the valdiation table through a manifest file, then
    #       this change will be a little more involved.
    # TODO: Consider not firing alerts if both expected value and actual values are both missing.
    #       Perhaps some dataset/datasource does not have checksum files and/or manifest files so
    #       we don't need to alert if values are missing, especially if the actual values were not
    #       computed as well (e.g. 2019_02.IP.DT.LUID.sorIP.dat.P1.gz)

    res = db_operation(connection,
        f'SELECT data_key, location, line_cnt_validated, checksum_validated, line_cnt_against, '
        f'checksum_against, line_cnt, checksum_value, last_modified_time, data_source, invalid_name '
        f'FROM data WHERE alert_time IS NULL AND (line_cnt_validated = 0 OR line_cnt_validated IS NULL '
        f'OR checksum_validated = 0 OR checksum_validated IS NULL) '
        f'ORDER BY archived_time ASC', [])
        #f'AND alert_time IS NULL ORDER BY archived_time ASC', [])

    logging.info(f'# records having issue: {len(res)}')

    for data_key, location, line_cnt_validated, checksum_validated, line_cnt_against, \
            checksum_against, line_cnt, checksum, mtime, datasource, invalid_name in res:

        if invalid_name:
            continue

        # It is known that PID data does not come with manifest and checksum files.
        # Hence we mute the alert for PID since no validation could be performed.
        if 'PID' in location:
            continue

        # tmp fix: do not send alert repetitively on old files, assuming we have inspected those spurious files and taken actions to process them separately.
        receiving_date = get_receiving_date_from_filename(location)
        #if receiving_date <= '20221203':
        #    continue
        #if '20220801' in data_key or '20220802' in data_key:
        #    continue

        logging.info('---------------------')
        logging.info(f'Processing file level validation alert: {location}')
        logging.info(f'line_cnt_validated: {line_cnt_validated}, checksum_validated: {checksum_validated}')

        # If file has just arrived, wait for data_file_grace_period days before processing alert.
        # This is so that we don't preemptively alert on files whose manifest or checksum file
        # has not arrived yet but may arrive later.
        deadline = mtime + timedelta(days=args['data_file_grace_period'])
        if datetime.now() - deadline <= timedelta(0):
            logging.info('too soon to check')
            continue

        #if _suppress_alert(deadline, datasource, data_key, checksum_validated, line_cnt_against):
        #    print('alert is suppressed')
        #    continue

        alert_msg = (f'[ ALERT - Data file ] Validation failed: {os.path.basename(location)}')

        if line_cnt_validated == 0 or line_cnt_validated is None:

            line_cnt_exp = db_operation(connection,
                'SELECT line_cnt FROM manifest_info WHERE location = %s AND data_key = %s',
                [line_cnt_against, data_key])
            line_cnt_exp = line_cnt_exp and line_cnt_exp[0][0]

            alert_msg += '\n(line count) '
            missing_value_msg = _generate_missing_value_alert_msg(
                'line count', 'manifest file', 'data file', 'expected', 'actual', line_cnt_exp, line_cnt)
            alert_msg += missing_value_msg or f'Expected: {line_cnt_exp} != Actual: {line_cnt}'
            logging.info(f'{alert_msg}')

        if checksum_validated == 0 or checksum_validated is None:

            checksum_exp = db_operation(connection,
                'SELECT value FROM checksum WHERE location = %s AND data_key = %s',
                [checksum_against, data_key])
            checksum_exp = checksum_exp and checksum_exp[0][0]

            alert_msg += '\n(checksum) '
            missing_value_msg = _generate_missing_value_alert_msg(
                'checksum', 'checksum file', 'data file', 'expected', 'actual', checksum_exp, checksum)
            alert_msg += missing_value_msg or f'Expected: {checksum_exp} != Actual: {checksum}'
            logging.info(f'{alert_msg}')


        if _send_alert(alert_msg):
            logging.info('Adding alert_time to data table...')
            db_operation(connection, f'UPDATE data SET alert_time = %s WHERE location = %s',
                         [datetime.now().replace(microsecond=0), location])

    # Process dataset level alerts
    logging.info('=========== DATASET LEVEL VALIDATION ALERTS ==========')

    res = db_operation(connection,
        f'SELECT data_source, data_set, snapshot, snapshot_dt, receiving_time, manifest_location, '
        f'integrity_validated, line_cnt_validated, checksum_validated '
        f'FROM validation WHERE alert_time IS NULL AND (integrity_validated = 0 '
        f'OR line_cnt_validated = 0 OR line_cnt_validated = 2 OR line_cnt_validated IS NULL '
        f'OR checksum_validated = 0 OR checksum_validated IS NULL) '
        f'ORDER BY receiving_time ASC', [])

    for data_source, data_set, snapshot, snapshot_dt, receiving_time, manifest_location, integrity_validated, line_cnt_validated, checksum_validated in res:

        print(f'[ ALERT - Data set ] Validation failed: {data_source}.{data_set} for {snapshot_dt}')
        alert_msg = (f'[ ALERT - Data set ] Validation failed: {data_source}.{data_set} for {snapshot_dt}')

        if integrity_validated == 0:

            if manifest_location is None:
                alert_msg += f'\n(integrity) Did not receive manifest file'
            else:
                alert_msg += f'\n(integrity) Did not receive all data files listed in manifest file {manifest_location}'

        if line_cnt_validated is None:
            alert_msg += f'\n(integrity) Did not receive manifest file'

        if line_cnt_validated == 2 and integrity_validated == 1:
            alert_msg += f'\n(line count) Did not validate line_cnt for some files:'
            data_files_with_no_line_count = db_operation(connection,
                f"SELECT data.data_key FROM manifest_tmp, data WHERE "
                f"manifest_tmp.data_source = %s AND manifest_tmp.data_set = %s AND "
                f"manifest_tmp.snapshot = %s AND manifest_tmp.snapshot_dt = %s AND "
                f"manifest_tmp.data_key = data.data_key AND data.line_cnt_validated IS NULL",
                [data_source, data_set, snapshot, snapshot_dt])
            for data_key in sorted(data_files_with_no_line_count):
                alert_msg += f'\n    {data_key}'
            alert_msg += '\n'

        if line_cnt_validated == 0:
            alert_msg += f'\n(line count) Validation failed for some files:'
            data_files_with_wrong_line_count = db_operation(connection,
                f'SELECT data.data_key FROM manifest_tmp, data WHERE '
                f'manifest_tmp.data_source = %s AND manifest_tmp.data_set = %s AND '
                f'manifest_tmp.snapshot = %s AND manifest_tmp.snapshot_dt = %s AND '
                f'manifest_tmp.data_key = data.data_key AND data.line_cnt_validated = 0', [data_source, data_set, snapshot, snapshot_dt])
            for data_key in sorted(data_files_with_wrong_line_count):
                alert_msg += f'\n    {data_key}'
            alert_msg += '\n'

        if checksum_validated is None and integrity_validated == 1:
            alert_msg += f'\n(checksum) Did not validate checksum for some files:'
            data_files_with_no_checksum = db_operation(connection,
                f'SELECT data.data_key FROM manifest_tmp, data WHERE '
                f'manifest_tmp.data_source = %s AND manifest_tmp.data_set = %s AND '
                f'manifest_tmp.snapshot = %s AND manifest_tmp.snapshot_dt = %s AND '
                f'manifest_tmp.data_key = data.data_key AND data.checksum_validated IS NULL', [data_source, data_set, snapshot, snapshot_dt])
            for data_key in sorted(data_files_with_no_checksum):
                alert_msg += f'\n    {data_key}'
            alert_msg += '\n'

        if checksum_validated == 0:
            alert_msg += f'\n(checksum) Validation failed for some files:'
            data_files_with_wrong_checksum = db_operation(connection,
                f'SELECT data.data_key FROM manifest_tmp, data WHERE '
                f'manifest_tmp.data_source = %s AND manifest_tmp.data_set = %s AND '
                f'manifest_tmp.snapshot = %s AND manifest_tmp.snapshot_dt = %s AND '
                f'manifest_tmp.data_key = data.data_key AND data.checksum_validated = 0', [data_source, data_set, snapshot, snapshot_dt])
            for data_key in sorted(data_files_with_wrong_checksum):
                alert_msg += f'\n    {data_key}'

        if _send_alert(alert_msg):
            logging.info('Adding alert_time to validation table...')
            db_operation(connection,
                         f'UPDATE validation SET alert_time = %s WHERE '
                         f'data_source = %s AND data_set = %s AND snapshot = %s AND snapshot_dt = %s',
                         [datetime.now().replace(microsecond=0), data_source, data_set, snapshot, snapshot_dt])

    ## EARLY RETURN AFTER GENERATING DATA FILE LEVEL VALIDATION ALERTS
    return


    res = db_operation(connection,
        'SELECT data_source, data_set, snapshot, snapshot_dt, receiving_time, total_line_cnt, '
        'manifest_location, total_line_count_validated, line_cnt_validated, checksum_validated, '
        'integrity_validated FROM validation ORDER BY receiving_time ASC', [])
    logging.info(f'# records in validation: {len(res)}')

    for datasource, dataset, snapshot, snapshot_dt, receiving_time, total_line_cnt, \
            manifest_location, total_line_count_validated, line_cnt_validated, checksum_validated, \
            integrity_validated in res:
        logging.info(f'Processing dataset level alert: {dataset} ({snapshot})')

        receiving_time = _normalize_receiving_time(receiving_time, manifest_location)
        if receiving_time is None:
            continue
        after_deadline, deadline = _is_after_transfer_deadline(datasource, dataset, receiving_time)
        print(after_deadline, deadline)

        if after_deadline:# and not _suppress_alert(deadline, datasource):
            # After deadline, retrieve next dataset snapshot
            _res = db_operation(connection,
                'SELECT snapshot, receiving_time, manifest_location, integrity_validated, '
                'total_line_count_validated, total_line_cnt FROM validation WHERE data_source = %s '
                'AND data_set = %s AND snapshot_dt > %s ORDER BY snapshot_dt ASC LIMIT 1',
                [datasource, dataset, snapshot_dt])

            if not _res:
                # No next snapshot after deadline, didn't receive expected snapshot in time
                _send_alert(f'[ ALERT - Dataset ] Transfer deadline missed: {dataset}\n(deadline) '
                            f'Last snapshot {snapshot} received on {receiving_time or "-"}, expected '
                            f'new snapshot by {deadline}')
                continue

            next_snapshot, next_receiving_time, next_manifest_location, next_integrity_validated, \
                next_total_line_count_validated, next_total_line_cnt =  _res[0]

            # Special handle fraudnet to not consider total_line_count_validated, otherwise
            # ensure that both total_line_count and integrity is validated
            if ((dataset.startswith('fraudnet') or dataset.startswith('ems')) and next_integrity_validated) or \
               (next_integrity_validated and next_total_line_count_validated):
                continue

            alert_msg = f'[ ALERT - Dataset ] Snapshot validation failed: {dataset}.{next_snapshot}'

            if next_manifest_location is None:
                # If manifest_location is None, it implies that the snapshot has multiple manifest
                # files and there is ambiguity in which one is actually associated with the snapshot.
                # This should be rare, but if the situation ever arises send out a special alert.
                res = db_operation(connection,
                    'SELECT manifest_location FROM manifest_tmp WHERE data_source = %s AND data_set = %s '
                    'AND snapshot = %s',
                    [datasource, dataset, next_snapshot])
                alert_msg += '\n(ambiguous manifest) ' + \
                                 ',\n                     '.join(
                                     [f'{os.path.basename(location)}' for location,
                                      in res[:MAX_ALERT_DISPLAY_LINES]])
                if len(res) > MAX_ALERT_DISPLAY_LINES:
                    alert_msg += f',\n                     ... ({len(res) - MAX_ALERT_DISPLAY_LINES} more)'

            elif not next_integrity_validated:
                res = db_operation(connection,
                    'SELECT data_key FROM manifest_tmp WHERE manifest_location = %s AND '
                    'data_location IS NULL',
                    [next_manifest_location])
                # If receiving_time is None, all files must be missing
                missing_type = '\n(missing file) ' if next_receiving_time else '\n(missing all) '
                alert_msg += missing_type + \
                             (',\n' + (len(missing_type) - 1) * ' ').join(
                                 [f'{data_key}' for data_key, in res[:MAX_ALERT_DISPLAY_LINES]])
                if len(res) > MAX_ALERT_DISPLAY_LINES:
                    alert_msg += f',\n               ... ({len(res) - MAX_ALERT_DISPLAY_LINES} more)'

            elif not next_total_line_count_validated:
                # Use the total_line_cnt from the previous valid snapshot if this snapshot is invalid
                if not ((total_line_count_validated or total_line_count_validated is None) and \
                        line_cnt_validated and checksum_validated and integrity_validated):
                    _res = db_operation(connection,
                        'SELECT total_line_cnt FROM validation WHERE data_source = %s AND data_set = '
                        '%s AND snapshot_dt < %s AND ((total_line_count_validated OR '
                        'total_line_count_validated IS NULL) AND line_cnt_validated AND '
                        'checksum_validated AND integrity_validated) ORDER BY snapshot_dt DESC LIMIT 1',
                        [datasource, dataset, snapshot_dt])
                    total_line_cnt = _res and _res[0][0]

                alert_msg += '\n(total line count) '
                missing_value_msg = _generate_missing_value_alert_msg(
                    'total line count', 'previous valid snapshot', 'this snapshot', 'reference', 'actual',
                    total_line_cnt, next_total_line_cnt)
                if next_total_line_cnt is not None and total_line_cnt is not None:
                    deviation = abs((next_total_line_cnt - total_line_cnt) * 100 / total_line_cnt) \
                                if total_line_cnt and next_total_line_cnt else None
                    alert_msg += f'Reference: {total_line_cnt or "-"}, ' \
                                 f'Actual: {next_total_line_cnt or "-"}' \
                                 f'{" ({:.2f}% deviation)".format(deviation) if deviation else ""}'
                else:
                    alert_msg += missing_value_msg


            _send_alert(alert_msg)

    # Process datasource level alerts

    # Datasource level unexpected dataset alerts

    logging.info('========== DATA SOURCE VALIDATION ALERTS ==========')
    res = db_operation(connection,
        'SELECT data_source, snapshot, receiving_time FROM validation_data_source '
        'ORDER BY receiving_time ASC', [])

    for datasource, snapshot, receiving_time in res:
        logging.info(f'Processing datasource level unexpected dataset alert: {datasource} ({snapshot})')

        # For unexpected dataset alerts, use receiving_time as deadline
        if receiving_time is None or _suppress_alert(receiving_time, datasource):
            continue

        # Check that each dataset of this datasource snapshot is accounted for in the dataset
        # list config file, if not issue an alert
        res = db_operation(connection,
            'SELECT data_set FROM validation WHERE data_source = %s AND snapshot = %s',
            [datasource, snapshot])
        actual_datasets = set([row for row, in res])
        expected_datasets = set(args['dataset_dict'][datasource])
        unexpected_datasets = actual_datasets - expected_datasets

        if unexpected_datasets:
            alert_msg = f'[ ALERT - Datasource ] Unexpected dataset(s) encountered: ' \
                        f'{datasource}.{snapshot}'
            alert_msg += '\n(unexpected dataset) ' + \
                         ',\n                     '.join(list(unexpected_datasets))
            alert_msg += f'\nConsider adding the dataset(s) listed above to ' \
                         f'{args["dataset_config_file"]}'
            _send_alert(alert_msg)

    # Datasource level deadline alerts (all datasets missed deadline)

    res = db_operation(connection,
        'SELECT data_source, snapshot, receiving_time AS data_source_receiving_time, (SELECT '
        'snapshot AS next_snapshot FROM validation_data_source WHERE vd.data_source = '
        'validation_data_source.data_source and validation_data_source.snapshot_dt '
        '> vd.snapshot_dt AND validation_data_source.receiving_time IS NOT NULL ORDER BY '
        'snapshot_dt ASC LIMIT 1) FROM validation_data_source vd ORDER BY receiving_time ASC', [])

    for datasource, snapshot, datasource_receiving_time, next_snapshot in res:
        logging.info(f'Processing datasource level missed deadline alert: {datasource} ({snapshot})')

        # Snapshot does not have receiving_time, can be more thorough by checking mtime of
        # manifests as well, for now just skip
        if datasource_receiving_time is None:
            continue

        dataset_info = []
        for dataset in args['dataset_dict'][datasource]:
            _res = db_operation(connection,
                'SELECT receiving_time, manifest_location FROM validation WHERE data_source = %s '
                'AND data_set = %s AND snapshot = %s',
                [datasource, dataset, snapshot])

            # _res can be [] if a dataset specified in the config file is missing from snapshot.
            # This datasource snapshot is invalid, so just skip out for now
            if not _res:
                break

            receiving_time, manifest_location = _res[0]
            receiving_time = receiving_time and _normalize_receiving_time(receiving_time,
                                                                          manifest_location)

            # receiving_time of dataset is missing, this case is not handled for now to be consistent
            # with dataset level alerting, so just skip out for now
            if not receiving_time:
                break

            after_deadline, deadline = _is_after_transfer_deadline(datasource, dataset, receiving_time)
            dataset_info.append((dataset, receiving_time, deadline))

            if after_deadline:
                # After deadline, retrieve the next dataset snapshot
                _res = db_operation(connection,
                    'SELECT 1 FROM validation WHERE data_source = %s AND data_set = %s AND snapshot = %s',
                    [datasource, dataset, next_snapshot])
                # Atleast one dataset of this datasource snapshot has arrived on time, no alert
                if _res:
                    break
            # The deadline has not passed for all datasets of this datasource snapshot yet, no alert
            else:
                break

        # Went through entire loop above without breaking, implies all datasets missing expected
        # snapshot after deadline, process alert
        else:
            dataset_info = list(zip(*dataset_info))
            max_idx = dataset_info[1].index(max(dataset_info[1]))
            # Suppress warning based on datasource deadline which is interpreted as the latest
            # deadline of its datasets
            if not _suppress_alert(dataset_info[2][max_idx], datasource):
                alert_msg  = f'[ ALERT - Datasource ] Transfer deadline missed: {datasource}'
                alert_msg += f'\n(deadline) Last dataset snapshot {dataset_info[0][max_idx]}.' \
                             f'{snapshot} received on {dataset_info[1][max_idx] or "-"},' \
                             f'\n           ALL datasets missed their respective deadlines'
                _send_alert(alert_msg)
