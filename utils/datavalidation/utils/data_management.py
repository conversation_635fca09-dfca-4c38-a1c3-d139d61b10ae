import boto3
import logging
import io
import os
import sys
import json
import gzip
import yaml
import bz2
from datetime import datetime, date
import subprocess
import traceback
import time
from argparse import ArgumentParser
import pathlib

from signalhub.datavalidation.utils.utilities import *


run_on_airflow = None
run_time_config = None
encryption_key = None # need to properly store and read the encryption key
gpg_key = None
gpg_passphrase = None
checksum_table_name = data_table_name = manifest_file_table_name = manifest_info_table_name = None


def check_converted_file_archive_status(conn, table, file):
    # TODO: Check return value when `bz2_file_archived_destination` is null
    db_return = db_operation(conn, f'SELECT bz2_file_archived_destination from {table} WHERE converted_file_location=%s', [file, ])
    if not db_return:
        return [()]
    else:
        return db_return[0]


def get_original_file_path_from_converted(conn, table, file):
    db_return = db_operation(conn, f'SELECT location from {table} WHERE converted_file_location=%s', [file, ])
    if not db_return:
        return None
    else:
        return db_return[0]


def check_archive_status(conn, table, file):
    db_return = db_operation(conn, f'SELECT archived_destination, archived_time from {table} WHERE location=%s', [file, ])

    if not db_return:
        return None, None
    else:
        return db_return[0]


def archive_file(conn, boto3_client, location, encrypt, tables, original_file_path=None):

    if dry_run:
        return

    max_boto3_transfer_tries = run_time_config['max_boto3_transfer_tries']

    ## specify archive location
    archive_bucket = run_time_config['archive_bucket']
    destination_path = location.replace(run_time_config['data_root'], run_time_config['archive_root'])
    source_bucket, source_key = get_bucket_and_key_from_filename(location)
    destination_bucket, destination_key = get_bucket_and_key_from_filename(destination_path)
    #key_location = destination_path.replace(f's3://{archive_bucket}/', '')
    logging.info(f'Archiving file to {destination_path}...')
    
    #boto3_client = create_boto3_client(storage_config)

    args = {'SSECustomerAlgorithm': 'AES256', 'SSECustomerKey': encryption_key}

    num_tries = 0
    while True:
        if num_tries == max_boto3_transfer_tries:
            logging.error(f'Unable to archive {location} after {num_tries} retries, exiting...')
            return

        try:
            num_tries = num_tries + 1
            #boto3_client.upload_file(location, archive_bucket, key_location, ExtraArgs=args if encrypt else None)
            #logging.info(f'source bucket: {source_bucket} === source key: {source_key}')
            #logging.info(f'dest bucket: {destination_bucket} === dest key: {destination_key}')

            if run_on_airflow:
                copy_file_on_s3_using_airflow(source_bucket, source_key, destination_bucket, destination_key)
                object_info = get_head_object_using_airflow(destination_bucket, destination_key, run_time_config['aws_conn_id'])
            else:
                copy_file_on_s3(source_bucket, source_key, destination_bucket, destination_key)
                object_info = get_head_object(boto3_client, destination_bucket, destination_key)

            version_id = object_info['VersionId']

        except Exception as e:
            logging.error(f'Failed to upload file to archive bucket due to {e}, '
                          f'retrying({num_tries}/{max_boto3_transfer_tries})...')
            continue

        logging.info(f'Successfully archived {location} to {destination_path}')

        # For archiving files, store the location of the archived converted file
        if original_file_path is not None:
            logging.info('set archive destination if it is converted')
            db_operation(conn, f'UPDATE {tables[0]} SET bz2_file_archived_destination=%s '
                               f'WHERE location=%s;', [destination_path, original_file_path])

        else:
            logging.info('set archive destination if it is original')
            for table in tables:
                db_operation(conn, f'UPDATE {table} SET archived_destination=%s, archived_time=%s, version_id=%s, '
                                   f'total_versions=total_versions+1 WHERE location=%s',
                             [destination_path,
                              object_info['LastModified'].strftime("%Y-%m-%d %H:%M:%S"), 
                              object_info['VersionId'], location])
                # Check if file is a data file by comparing table name equal data table name
                #if table == data_table_name and ('.bz2' in destination_path.split('/')[-1] or any(x in destination_path for x in ['cc1_', 'PID_'])):
                #    bz2_archived_file = destination_path
                #    if 'cc1_' in bz2_archived_file:
                #        bz2_archived_file = bz2_archived_file.replace('.gpg', '')
                #    if 'PID_' in bz2_archived_file:
                #        bz2_archived_file = bz2_archived_file.replace('.zip', '.bz2')
                #    db_operation(conn, f'UPDATE {table} SET bz2_file_archived_destination=%s WHERE location=%s AND '
                #                       f'bz2_file_archived_destination IS NULL', [bz2_archived_file, location])

        return


def is_inquiry_request_file(file_full_path):
    is_in_inquiry_directory = run_time_config['laas_inquiry_sts_landing_dir'] in os.path.dirname(file_full_path)
    is_bz2 = '.bz2' in os.path.basename(file_full_path)
    return is_bz2 and is_in_inquiry_directory


def get_trigger_file_path_from_inquiry_file_path(inquiry_file_path):
    # Path Examples:
    # inquiry_file_path: /local2/sts_data/us/ems/client_test_files/pandora.20190303000000.20190303000000.0.bz2.20190304332201
    # inquiry_file_subdir: ems/client_test_files/pandora.20190303000000.20190303000000.0.bz2.20190304332201
    # _trigger_file_full_path: /local2/copy_workflow_run/trigger_files/ems/client_test_files/pandora.20190303000000.20190303000000.0.bz2.20190304332201.inprogress

    inquiry_file_subdir = inquiry_file_path.replace(run_time_config['data_root'], '')
    trigger_file_full_path = '{}/{}.{}'.format(run_time_config['inquiry_trigger_files_root'], inquiry_file_subdir,
                                               'inprogress')
    return trigger_file_full_path


def process_inquiry_file(location, archived_path):
    logging.info(f'Found and archived inquiry file{location}, creating trigger files for data copy workflow..')
    trigger_file_full_path = get_trigger_file_path_from_inquiry_file_path(inquiry_file_path=location)
    trigger_file_dir = os.path.dirname(trigger_file_full_path)

    # create dir for trigger files if dir not exists
    pathlib.Path(trigger_file_dir).mkdir(parents=True, exist_ok=True)

    # create trigger file, remove .inprogress suffix after creation
    with open(trigger_file_full_path, 'w+') as f:
        f.write(archived_path)
    logging.info(f'successfully created trigger file {trigger_file_full_path} for {location}')
    os.rename(trigger_file_full_path, trigger_file_full_path.replace('.inprogress', ''))


def check_file_status(conn, table, file, file_obj):
    file_missing = file_changed = False
    try:
        #file_size = os.path.getsize(file)
        #last_modified_time = datetime.fromtimestamp(os.path.getmtime(file)).replace(microsecond=0)
        file_size = file_obj['Size']
        last_modified_time = file_obj['LastModified'].strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        logging.warning(f'File {file} does not exist anymore: {e}')
        file_missing = True

    exist_as_original_file = db_operation(conn, f'SELECT location, file_size, last_modified_time, archived_destination '
                                                f'from {table} WHERE location=%s', [file, ])
    exist_as_converted_file = db_operation(conn, f'SELECT location from {table} WHERE converted_file_location=%s',
                                           [file, ]) if table == data_table_name else False

    file_in_db = True if (exist_as_original_file or exist_as_converted_file) else False
    if file_in_db and not file_missing and exist_as_original_file:
        (_, file_size_in_db, last_modified_time_in_db, archived_destination) = exist_as_original_file[0]
        last_modified_time_in_db = last_modified_time_in_db.strftime("%Y-%m-%d %H:%M:%S")
        print('status check:', file_size, file_size_in_db, last_modified_time, last_modified_time_in_db)
        file_changed = file_size != file_size_in_db or last_modified_time != last_modified_time_in_db

    # file missing, file exist not in db, file exist in db consistent, file exist in db consistent
    return file_missing, file_in_db, file_changed, exist_as_converted_file


def process_cc2_data_file(conn, boto3_client, file, file_obj):
    '''
    For CC2 files, only perform most basic check, no need to insert into inventory DB
    '''
    ## check data corruption
    #line_count = calculate_line_count_using_aws_cli(file, creds=run_time_config['credentials'], mem_dir=run_time_config['mem_dir'])
    #if line_count is None:
    #    move_file_on_s3_using_airflow(boto3_client, file, run_time_config["malformed_file_folder"], run_time_config['aws_conn_id'])
    #    logging.warning(f'This file likely corrupted and has been moved to malformed file folder...')
    #    return

    ## convert to bz2
    #converted_file, converted_file_checksum = convert_file_to_bz2_using_aws_cli(file, creds=run_time_config['credentials'], mem_dir=run_time_config['mem_dir'], calculate_converted_file_checksum=False)
    #if converted_file is None:
    #    move_file_on_s3_using_airflow(boto3_client, file, run_time_config["malformed_file_folder"], run_time_config['aws_conn_id'])
    #    logging.warning(f'This file likely corrupted and has been moved to malformed file folder...')
    #    return
    #logging.info(f'converted_file_checksum: {converted_file_checksum}')

    ## archive raw file
    archive_bucket = run_time_config['archive_bucket']
    destination_path = file.replace(run_time_config['data_root'], run_time_config['archive_root'])
    source_bucket, source_key = get_bucket_and_key_from_filename(file)
    destination_bucket, destination_key = get_bucket_and_key_from_filename(destination_path)
    logging.info(f'Archiving file to {destination_path}...') 
    try:
        #copy_file_on_s3_using_airflow(source_bucket, source_key, destination_bucket, destination_key, run_time_config['aws_conn_id'])
        copy_file_on_s3_using_airflow(source_bucket, source_key, destination_bucket, destination_key)
    except Exception as e:
        logging.error(f'Failed to upload file to archive bucket due to {e}.')

    return


def process_data_file(conn, boto3_client, file, file_obj):

    def _send_alert(alert_msg):
        status_code = send_notification(alert_msg, run_time_config['ms_teams_channel_url'], https_proxy_url=run_time_config['https_proxy_url'], http_proxy_url=run_time_config['http_proxy_url'])
        if status_code != requests.codes.ok:
            return False
        return True

    def _validate_data_file_name():
        filename = os.path.basename(file)
        if 'da/PID' in file:
            return True
        elif filename.startswith('cc1_') and len(filename.split('.')) == 8:
            return True
        elif filename.startswith('gvap') or filename.startswith('creditheader'):
            return len(filename.split('.')) in [4, 5, 6]
        else:
            return len(filename.split('.')) in [5, 6]

    def _is_checksum_populated():
        db_return = db_operation(conn, f'SELECT checksum_value from {data_table_name} WHERE location=%s ', [file, ])
        return True if db_return[0][0] is not None else False

    def _is_linecount_populated():
        db_return = db_operation(conn, f'SELECT line_cnt from {data_table_name} WHERE location=%s ', [file, ])
        return True if db_return[0][0] is not None else False

    file_missing, file_in_db, file_changed, file_exist_as_converted_file = check_file_status(conn, data_table_name, file, file_obj)
    logging.info(f'file status: {file_missing}, {file_in_db}, {file_changed}, {file_exist_as_converted_file}')

    if file_missing:
        return

    if file_exist_as_converted_file:
        archived_destination = check_converted_file_archive_status(conn, data_table_name, file)
        if not archived_destination[0]:
            original_file_path = get_original_file_path_from_converted(conn, data_table_name, file)[0]
            archive_file(conn, boto3_client, file, encrypt=False, tables=[data_table_name], original_file_path=original_file_path)

    elif (not file_in_db) or (file_in_db and file_changed):

        preprocessed_file= file

        ## Decrypt the file if it is gpg encrypted
        if '.gpg' in file or '.pgp' in file:
            if run_on_airflow:
                decrypted_file = decrypt_gpg_file_using_aws_cli(file, gpg_key, gpg_passphrase, run_time_config['credentials'], run_time_config['mem_dir'])
            else:
                # using Python package to decrypt
                decrypted_file = decrypt_gpg_file(boto3_client, file, gpg_key, gpg_passphrase)
            if decrypted_file:
                preprocessed_file = decrypted_file
            else:
                move_file_on_s3_using_airflow(boto3_client, file, run_time_config["malformed_file_folder"], run_time_config['aws_conn_id'])
                logging.warning(f'This file is not decryptable and has been moved to malformed file folder...')
                if _send_alert(f'Failed to decrypt data file {file}'):
                    logging.info('Alert sent')
                return

        ## Unzip and compress the file if it is zipped
        if '.zip' in preprocessed_file:
            unzipped_file = unzip_file_using_aws_cli(boto3_client, preprocessed_file, run_time_config['credentials'], run_time_config['mem_dir'])
            if unzipped_file:
                preprocessed_file = unzipped_file
            else:
                move_file_on_s3_using_airflow(boto3_client, file, run_time_config["malformed_file_folder"], run_time_config['aws_conn_id'])
                logging.warning(f'This file is not unzippable and has been moved to malformed file folder...')
                if _send_alert(f'Failed to unzip data file {file}'):
                    logging.info('Alert sent')
                return

        # Extract base metadata from this file
        base_metadata = extract_metadata_base(file, file_obj)

        # Check if file following our partition #
        if _validate_data_file_name():
            base_metadata['invalid_name'] = 0
            metadata_from_file_name = extract_metadata_from_file_name(file)
        else:
            base_metadata['invalid_name'] = 1
            logging.warning('File does not match with correct naming format')
            metadata_from_file_name = {}

        metadata = {**base_metadata, **metadata_from_file_name}
        #logging.info(metadata)

        db_operation(conn, f'INSERT INTO {data_table_name}(%s) VALUES(%s) ON CONFLICT(location) DO UPDATE SET %s'
                           % (','.join(list(metadata.keys())),
                              ','.join(['%s'] * len(metadata)),
                              ','.join([k + '=excluded.' + k for k in metadata.keys()])),
                           list(metadata.values()))

        ## if the preprocessed file is already in bz2, archive it and update converted_file_location and converted_file_checksum
        #if ('.gpg' in file and decrypted_file) or ('.zip' in file and unzipped_file):
        if preprocessed_file != file and '.bz2' in preprocessed_file:
            db_operation(conn, f'UPDATE {data_table_name} SET converted_file_location=%s '
                               f'WHERE location = %s;',
                         [preprocessed_file, file])

            logging.info(f'Calculating checksum of preprocessed bz2 file {preprocessed_file}')
            converted_file_checksum = calculate_checksum_using_aws_cli(preprocessed_file, creds=run_time_config['credentials'], mem_dir=run_time_config['mem_dir'])
            db_operation(conn, f'UPDATE {data_table_name} SET converted_file_checksum=%s '
                                   f'WHERE location = %s;',
                             [converted_file_checksum, file])
            ## archive preprocessed file
            logging.info('Archiving preprocessed file')
            archive_file(conn, boto3_client, preprocessed_file, encrypt=False, tables=[data_table_name], original_file_path=file)

        ## calculate checksum on original file and calculate line count on the preprocessed file
        if not metadata['invalid_name']:
            checksum, line_count = calculate_checksum_and_line_count_using_aws_cli(boto3_client, file, preprocessed_file=preprocessed_file, creds=run_time_config['credentials'], mem_dir=run_time_config['mem_dir'])
            logging.info(f'CHECKSUM: {checksum}')
            logging.info(f'LINE COUNT: {line_count}')
            db_operation(conn, f'UPDATE {data_table_name} SET checksum_value=%s WHERE location=%s',
                         [checksum, file])
            db_operation(conn, f'UPDATE {data_table_name} SET line_cnt=%s WHERE location=%s', [line_count, file])

        # Convert non .bz2 files to .bz2 and archive the converted file
        if any(x in preprocessed_file for x in ['.gz', '.pkzip']):
            if not dry_run:
                logging.info(f"Found non-bz2 format file {file}, converting...")
                converted_file, converted_file_checksum = convert_file_to_bz2_using_aws_cli(preprocessed_file, creds=run_time_config['credentials'], mem_dir=run_time_config['mem_dir'], calculate_converted_file_checksum=True)
                logging.info(f'CHECKSUM OF CONVERTED: {converted_file_checksum}')
                db_operation(conn, f'UPDATE {data_table_name} SET converted_file_location=%s, converted_file_checksum=%s '
                                   f'WHERE location = %s;',
                             [converted_file, converted_file_checksum, file])
                archive_file(conn, boto3_client, converted_file, encrypt=False, tables=[data_table_name], original_file_path=file)
                # delete decrypted non .bz2 file since they are not needed eventually
                if '.gpg' in file or '.pgp' in file:
                    remove_file(boto3_client, decrypted_file)
            else:
                logging.info(f"[DRY_RUN]: Found non-bz2 format file {file}.")

        # archive original file
        archive_file(conn, boto3_client, file, encrypt=False, tables=[data_table_name])

    else: # file not changed
        logging.info('ELSE...')
        if _validate_data_file_name() and '.gpg' not in file and '.pgp' not in file and '.zip' not in file:
            is_checksum_populated = _is_checksum_populated()
            is_linecount_populated = _is_linecount_populated()

            if not is_checksum_populated or not is_linecount_populated:
                checksum, line_count = calculate_checksum_and_line_count_using_aws_cli(boto3_client, file, creds=run_time_config['credentials'], mem_dir=run_time_config['mem_dir'])
                logging.info(f'LINE COUNT: {line_count}, CHECKSUM: {checksum}')
            if not is_checksum_populated:
                db_operation(conn, f'UPDATE {data_table_name} SET checksum_value=%s WHERE location=%s', [checksum, file])
            if not is_linecount_populated:
                db_operation(conn, f'UPDATE {data_table_name} SET line_cnt=%s WHERE location=%s', [line_count, file])

        archived_destination, archived_time = check_archive_status(conn, data_table_name, file)
        if not archived_destination:
            logging.info('Has not been archived yet though it is in db, archiving it...')
            archive_file(conn, boto3_client, file, encrypt=False, tables=[data_table_name])


def process_checksum_file(conn, boto3_client, file, file_obj):

    def _is_corrupted_checksum():
        try:
            file_lines = boto3_client.get_object(Bucket=get_bucket_name_from_filename(file), Key=file_obj['Key'])["Body"].read().decode('utf-8').splitlines()
            return False
        except:
            return True

    def _is_malformed_content(metadata):
        # This method will determine whether one line of the checksum content is valid or malformed
        if "data_key" not in metadata.keys():
            # In _extract_metadata_checksum(), if one line of content is malformed, it won't be extracted
            return True
        return False

    def _extract_metadata_checksum():

        base_info = extract_metadata_base(file, file_obj)
        base_info = {'location': base_info['location'], 'xfer_time': base_info['xfer_time'],
                     'last_modified_time': base_info['last_modified_time'], 'file_size': base_info['file_size']}

        file_lines = boto3_client.get_object(Bucket=get_bucket_name_from_filename(file), Key=file_obj['Key'])["Body"].read().decode('utf-8').splitlines()

        results = []
        for line in file_lines:
            line = line.strip()
            if not line:
                continue
            tmp_result = dict()
            if os.path.basename(file).startswith('truvue'):
                tmp_result['value'] = line[:32].upper()
                tmp_result['data_key'] = os.path.basename(file).rsplit('.', 1)[0].replace('chksum', 'pkzip')
            else:
                tmp_result['value'] = line.split(' ', 1)[0].upper()
                if '*' in line:
                    tmp_result['data_key'] = line.rsplit('*', 1)[1].strip()
                elif ' ' in line:
                    tmp_result['data_key'] = line.rsplit(' ', 1)[1].strip()
                else:
                    logging.warning('Malformated checksum file, no valid delimiter found: %s' % file)
                    continue

                # e.g. /local2/inventory_test/cis/rentbureau/RB_OEIDP.20180505000000.20180507194400.1.bz2.md5
                if tmp_result['data_key'] == '-':
                    logging.warning('Malformated data_key in checksum file: %s' % file)
                    continue

                if '/' in tmp_result['data_key']:
                    tmp_result['data_key'] = tmp_result['data_key'].rsplit('/', 1)[1].strip()

            # CEM checksum format changed, crc is handled here also
            tmp_result['type'] = 'crc' if '.crc32sum' in file else 'md5'
            results.append({**base_info, **tmp_result})

        results = [base_info] if not results else results
        return results

    file_missing, file_in_db, file_changed, file_exist_as_converted_file = check_file_status(conn, checksum_table_name, file, file_obj)
    logging.info(f'file status: {file_missing}, {file_in_db}, {file_changed}, {file_exist_as_converted_file}')

    if file_missing:
        return

    if not file_in_db or (file_in_db and file_changed):

        if _is_corrupted_checksum():
            # Edge case: corrupted content. Not readable
            move_file_on_s3_using_airflow(boto3_client, file, run_time_config["malformed_file_folder"], run_time_config['aws_conn_id'])
            logging.warning(f'This file is likely corrupted and  has been moved to malformed file folder...')
            return

        metadata_list = _extract_metadata_checksum()
        # function _extract_metadata_checksum() will extract the base info, and combine with correct formated content
        # If metadata_list only contains one element and this element does not have key 'data_key',
        # it means the checksum file is malformed, and this only one element is the base info

        logging.info(f'insert or update record for {file} into TABLE: {checksum_table_name}')
        for metadata in metadata_list:
            if _is_malformed_content(metadata):
                # Edge case: malformed content. All lines of content are malformed
                logging.warning(f'Found malformed file {file}, moving it to malformed file folder...')
                move_file_on_s3_using_airflow(boto3_client, file, run_time_config["malformed_file_folder"], run_time_config['aws_conn_id'])
                #move_file(file, run_time_config["malformed_file_folder"])
                logging.warning(f'Malformed file {file} has been moved to malformed file folder...')
                return

            else:
                # Normal case: The checksum file contains at least one line of valid checksum content
                db_operation(conn, f'INSERT INTO {checksum_table_name}(%s) VALUES(%s) ON CONFLICT(location, data_key) '
                                   f'DO UPDATE SET %s'
                                   % (','.join(list(metadata.keys())),
                                      ','.join(['%s'] * len(metadata)),
                                      ','.join([k + '=excluded.' + k for k in metadata.keys()])),
                                   list(metadata.values()))

        archive_file(conn, boto3_client, file, encrypt=False, tables=[checksum_table_name])

    else:  # file not changed
        archived_destination, archived_time = check_archive_status(conn, checksum_table_name, file)
        logging.info(f'archive status: {archived_destination}, {archived_time}')
        if not archived_destination:
            archive_file(conn, boto3_client, file, encrypt=False, tables=[checksum_table_name])


def process_manifest_file(conn, boto3_client, file, file_obj):

    if 'ecs_membership' in file and 'landing' in get_bucket_name_from_filename(file):
        decoding_type = 'utf-16'
    else:
        decoding_type = 'utf-8'

    def _is_corrupted_manifest():
        try:
            file_lines = boto3_client.get_object(Bucket=get_bucket_name_from_filename(file), Key=file_obj['Key'])["Body"].read().decode(decoding_type).splitlines()
            return False
        except:
            return True

    def _extract_metadata_manifest():

        # load the manifest file
        file_lines = boto3_client.get_object(Bucket=get_bucket_name_from_filename(file), Key=file_obj['Key'])["Body"].read().decode(decoding_type).splitlines()

        lc_dict = dict()
        crc_dict = dict()
        #with open(file) as f:
        for line in file_lines:

            #logging.info(f'LINE : {line}')

            line = line.strip()

            if not line:
                logging.warning(f'Empty line in manifest file: {file}')
                continue

            # e.g. /local2/inventory_test/gfid/fraudnet_2co/fraudnet_2co.20180528.manifest
            if ' ' in line and line.split(' ')[1] == '-':
                logging.warning(f'Malformated line in manifest file: {file}: {line}')
                continue

            line_split = line.split('|') if '|' in line else [line]
            # abnormal case: fn
            if len(line_split) == 1:
                logging.warning(f'file {file} does not have corresponding line count for data_key')
                lc_dict[line_split[0]] = None
            # normal case: fn|lc
            elif len(line_split) == 2:
                lc_dict[line_split[0]] = line_split[1]
            # cem case: fn|crc|lc
            elif len(line_split) == 3:
                lc_dict[line_split[0]] = line_split[2]
                crc_dict[line_split[0]] = line_split[1].upper()
            else:
                logging.warning(f'Malformated line in manifest file: {file}: {line}')

        #logging.info(f'{lc_dict}')
        #logging.info(f'{crc_dict}')

        return lc_dict, crc_dict

    file_missing, file_in_db, file_changed, file_exist_as_converted_file = check_file_status(conn, manifest_file_table_name, file, file_obj)
    logging.info(f'{file_missing}, {file_in_db}, {file_changed}, {file_exist_as_converted_file}')

    if file_missing:
        return

    if not file_in_db or (file_in_db and file_changed):

        if _is_corrupted_manifest():
            # Edge case: corrupted content. Not readable
            move_file_on_s3_using_airflow(boto3_client, file, run_time_config["malformed_file_folder"], run_time_config['aws_conn_id'])
            logging.warning(f'This file is likely corrupted and  has been moved to malformed file folder...')
            return

        lc_dict, crc_dict = _extract_metadata_manifest()
        meta_manifest = {**extract_metadata_base(file, file_obj), **extract_metadata_from_file_name(file)}
        #logging.info(f'{meta_manifest}')
        #logging.info(f'insert or update record for {file} into TABLE: {manifest_file_table_name}')

        # populate manifest_file table
        cols = ('location', 'manifest_key', 'xfer_time', 'last_modified_time', 'file_size',
                'data_source', 'data_set', 'snapshot', 'snapshot_dt')
        db_operation(conn, f'INSERT INTO {manifest_file_table_name} (%s) VALUES(%s) ON CONFLICT(location) '
                           f'DO UPDATE SET %s'
                           % (','.join(cols), ','.join(['%s'] * len(cols)), ','.join(c + '=excluded.' + c for c in cols)),
                           [meta_manifest['location'], meta_manifest['data_key'], meta_manifest['xfer_time'],
                            meta_manifest['last_modified_time'], meta_manifest['file_size'], meta_manifest['data_source'],
                            meta_manifest['data_set'], meta_manifest['snapshot'], meta_manifest['snapshot_dt']])

        # populate manifest_info table
        for k in lc_dict.keys():
            #logging.info(f'insert line count for {k}')
            cols = ('location', 'data_key', 'line_cnt')
            db_operation(conn, f'INSERT INTO {manifest_info_table_name}(%s) VALUES(%s) ON CONFLICT(location,data_key) '
                               f'DO UPDATE SET %s'
                               % (','.join(cols), ','.join(['%s'] * len(cols)), ','.join(c + '=excluded.' + c for c in cols)),
                               [meta_manifest['location'], k, lc_dict[k]])

        for k in crc_dict.keys():
            cols = ('location', 'data_key', 'file_size', 'xfer_time', 'value', 'type', 'last_modified_time')
            db_operation(conn, f'INSERT INTO {checksum_table_name}(%s) VALUES(%s) ON CONFLICT(location, data_key) '
                               f'DO UPDATE SET %s'
                               % (','.join(cols), ','.join(['%s'] * len(cols)), ','.join(c + '=excluded.' + c for c in cols)),
                               [meta_manifest['location'], k, meta_manifest['file_size'], meta_manifest['xfer_time'],
                                crc_dict[k], 'crc', meta_manifest['last_modified_time']])

        m_tables = [manifest_file_table_name] if not crc_dict else [manifest_file_table_name, checksum_table_name]
        archive_file(conn, boto3_client, file, encrypt=False, tables=m_tables)
    else: #file not changed
        archived_destination, archived_time = check_archive_status(conn, manifest_file_table_name, file)
        if not archived_destination:
            lc_dict, crc_dict = _extract_metadata_manifest()
            m_tables = [manifest_file_table_name] if not crc_dict else [manifest_file_table_name, checksum_table_name]
            archive_file(conn, boto3_client, file, encrypt=False, tables=m_tables)


def scan_process_file(conn, boto3_client):
    # Scan all files on landing zone

    bucket = get_bucket_name_from_filename(run_time_config['data_root'])
    prefix = run_time_config['data_root'].replace(f's3://{bucket}/', '')
    logging.info(f'Traversing {bucket}/{prefix}')
    paginator = boto3_client.get_paginator('list_objects_v2')
    pages = paginator.paginate(Bucket=bucket, Prefix=prefix)
    all_content_list = []
    for page in pages:
        all_content_list += page['Contents']
    all_content_list = [obj for obj in all_content_list if os.path.basename(obj['Key']) != '']
    logging.info(f'# files in total: {len(all_content_list)}')

    content_list = all_content_list
    logging.info(f'# files to scan: {len(content_list)}')

    num_eligible_file = 0
    num_data_file_processed, num_manifest_file_processed, num_checksum_file_processed = 0, 0, 0

    for obj in content_list:

        full_location = f's3://{bucket}/' + obj['Key']

        # skip irrelevant files
        identifiers_of_files_to_exclude = ['keep', '.txt', '.inprogress', 'crosscore_v2.x/test/', 'third_party', 'adhoc', 'laas_output']
        if any(x in full_location for x in identifiers_of_files_to_exclude) and '.manifest' not in full_location:
            continue

        if 'crosscore_v2.x/' in full_location and all([x not in full_location for x in ['prod/', 'prod_op/']]):
            continue

        # skip the file that was received out of the required time range
        #landing_date_time = obj['LastModified']
        #landing_date = f'{landing_date_time.year}{str(landing_date_time.month).zfill(2)}{str(landing_date_time.day).zfill(2)}'
        receiving_date = get_receiving_date(full_location, obj)
        if not receiving_date:
            logging.warning(f'Cannot determine receiving date for file {full_location}.')
            continue

        if receiving_date < run_time_config['start_date'] or receiving_date > run_time_config['end_date']:
            logging.warning(f'{full_location} is not in the time range for processing.')
            continue

        # skip object that is a directory
        if obj['Size'] == 0:
            continue

        #if num_data_file_processed + num_checksum_file_processed > 10:
        #    break

        # ad-hoc for CEM
        #if 'cem_' in full_location and ('.bz2' in full_location or 'cell' in full_location):
        #    continue

        #if full_location != 's3://signal-hub-landing-365007379098-us-east-1/us/cis/cem/cem_clarity.3683_20221216090141.20221216093313.1.gz':
        #    continue

        #if 'cem_clarity' not in full_location or receiving_date < '20221216':
        #    continue

        #if num_data_file_processed > 5:
        #    continue

        # skip data sources with very large file
        #if any(x in full_location for x in ['auto/nvdb', 'source_emailinsight']):
        #    continue

        num_eligible_file += 1

        # Begin to process this file
        logging.info('====== Begin to process %s' % full_location)
        try:
            if any(x in full_location for x in ['.md5', '.chksum', '.checksum', '.crc32sum']):
                logging.info('------ PROCESS CHECKSUM FILE')
                process_checksum_file(conn, boto3_client, full_location, obj)
                num_checksum_file_processed += 1
            elif any(x in full_location for x in ['.bz2', '.gz', '.pkzip', '.zip']):
                logging.info('------ PROCESS DATA FILE')
                process_data_file(conn, boto3_client, full_location, obj)
                num_data_file_processed += 1
            elif '.manifest' in full_location:
                logging.info('------ PROCESS MANIFEST FILE')
                process_manifest_file(conn, boto3_client, full_location, obj)
                num_manifest_file_processed += 1
            elif 'crosscore_v2' in full_location:
                logging.info('------ PROCESS CC2 FILE ======')
                process_cc2_data_file(conn, boto3_client, full_location, obj)
                num_data_file_processed += 1
            else:
                logging.info('%s is not the type of file we want to process, skipping to next...' % full_location)
        except Exception as e:
            logging.error('Error occurred while processing %s as %s, will pass on to next file, %s'
                          % (full_location, e, traceback.format_exc()))
            print(traceback.format_exc())
            continue

    logging.info(f'{num_eligible_file} files can be processed')
    logging.info(f'{num_data_file_processed} data files processed')
    logging.info(f'{num_manifest_file_processed} manifest files processed')
    logging.info(f'{num_checksum_file_processed} checksum files processed')


def preprocess_manifest_files(db_conn, boto3_client, manifest_path, dest_path):

    def get_manifest_filename(filename, line):
        # Example valid line: cem_clarity.2308_20190220090209.20190220091853.1.gz|7861766f|57827040
        dataset = line.split('.')[0]
        # Example valid filename: manifest.20190220.1372091.txt.20190220093611
        fields = filename.split('.')
        # Example output filename: cem_clarity.20190220.1372091.manifest.20190220093611
        return '.'.join([dataset, fields[1], fields[2], 'manifest', fields[4]])

    logging.info(f'Preprocessing CEM manifest files at: {manifest_path}')

    bucket, prefix = get_bucket_and_key_from_filename(manifest_path)
    content_list = boto3_client.list_objects(Bucket=bucket, Prefix=prefix)['Contents']
    logging.info(f'# files to scan: {len(content_list)}')

    # Process CEM manifest files at manifest_path and aggregate their contents into a dictionary
    processed_dict = {}
    #for path, subdirs, files in os.walk(manifest_path):
    for obj in content_list:
        full_path = bucket + '/' + obj['Key']
        filename = os.path.basename(obj['Key'])
        try:
            obj_read = boto3_client.get_object(Bucket=bucket, Key=obj['Key'])
            with io.BytesIO(obj_read["Body"].read()) as tf:
                for l in tf.readlines():
                    l = l.strip().decode("utf-8")
                    try:
                        manifest_filename = get_manifest_filename(filename, l.strip())
                    except Exception as e:
                        logging.warning(f'Malformated manifest file {filename}-{l.strip()}: {e}'
                                        f', skipping...')
                    else:
                        if manifest_filename not in processed_dict:
                            processed_dict[manifest_filename] = []
                        processed_dict[manifest_filename].append(l)
        except Exception as e:
            logging.warning(f'Failed to process manifest file {full_path}: {e}')

    print(processed_dict)

    # Write to new processed manifest files
    for manifest_filename, content in processed_dict.items():
        dest = os.path.join(dest_path, manifest_filename.split('.', 1)[0], manifest_filename)
        print(dest)
        dest_bucket, dest_key = get_bucket_and_key_from_filename(dest)
        # Already inventoried, no need to preprocess again
        if db_operation(db_conn, f'SELECT location FROM {manifest_file_table_name} WHERE location = %s', [dest]):
            continue
        logging.info(f'Writing processed contents to new manifest file at: {dest}...')
        dest_info = boto3_client.list_objects(Bucket=dest_bucket, Prefix=dest_key)
        if 'Contents' not in dest_info or dest_info['Contents'][0].get('Size', 0) == 0:
            try:
                boto3_client.put_object(Bucket=dest_bucket, Key=dest_key, Body=''.join(content).encode('utf-8'))
            except Exception as e:
                logging.warning(f'Failed to write new manifest contents to {dest}: {e}, skipping...')
        else:
            print('Non-empty manifest file already exists at the write destination')
            logging.warning(f'Non-empty manifest file already exists at the write destination {dest}, skipping...')


def run_data_management(db_conn, boto3_client, config, run_on_airflow_flag=True, dry_run_flag=False):

    global run_on_airflow, run_time_config, dry_run
    run_on_airflow = run_on_airflow_flag
    run_time_config = config
    dry_run = dry_run_flag
    print('run time config:', run_time_config)

    # set up logging
    create_local_directory(run_time_config['log_dir'])
    if not run_on_airflow_flag:
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
    start = time.time()
    logging.basicConfig(filename='%s/data_management.log.%s'
                                 % (run_time_config['log_dir'], datetime.utcfromtimestamp(start).strftime('%Y%m%d')),
                        level=logging.DEBUG, filemode='w', format='%(asctime)s %(name)s - %(levelname)s - %(message)s',
                        datefmt='%Y-%m-%d %H:%M:%S')
    config_logging_level(logging)
    logging.info('====== Start data_management.py ======')
    logging.info('====== GOOG LUCK! ======')

    global checksum_table_name, data_table_name, manifest_file_table_name, manifest_info_table_name
    checksum_table_name, data_table_name, manifest_file_table_name, manifest_info_table_name = \
        run_time_config['checksum_table_name'], run_time_config['data_table_name'], \
        run_time_config['manifest_file_table_name'], run_time_config['manifest_info_table_name']
    global gpg_key, gpg_passphrase
    gpg_key = run_time_config['gpg_key']
    gpg_passphrase = run_time_config['gpg_passphrase']

    #if run_time_config['project'] == 'oeidp':
    #    preprocess_manifest_files(db_conn, boto3_client, run_time_config['data_root'] + 'cis/manifest_cem', run_time_config['data_root'] + 'cis/')

    logging.info('========== MAIN JOB ==========')
    scan_process_file(db_conn, boto3_client)
    logging.info('========== MAIN JOB DONE ==========')
    db_conn.close()

    logging.info('[RUN TIME STATS]: Running data_management.py against %s took %s seconds.'
                 % (run_time_config['data_root'], time.time() - start))


if __name__ == '__main__':

    print('======  start ======')
    parser = ArgumentParser(description='Execute data management against landing zone based on input config file')
    parser.add_argument('--config', dest='config_file_path', required=True,
                        help='Path to the data_management config file')
    parser.add_argument('--dry_run', dest='dry_run_flag', default=False,
                        help='Boolean value indicating if this is a dry run, dry run will not calculate '
                             'line count/checksum and not archive')
    args = parser.parse_args()

    postgres_db_config = {'hostname': 'datavalidation-zsddkcgtk35qfxw9.c9qafslrwnv1.us-east-1.rds.amazonaws.com', 'login':'postgres', 'password':'8SdnH28TDeEx6n2NSQVALIDwCVxq2osFaT4DIc762hyKVF8glqk2KfQRRMQilnDK'}
    db_conn = create_connection(postgres_db_config['hostname'], postgres_db_config['login'], postgres_db_config['password'])
    res = db_operation(db_conn, 'select count(*) from data', [])
    print('CHECK DB CONNECTION:', res)

    boto3_client = boto3.client('s3', aws_access_key_id='********************', aws_secret_access_key='E3A79gAqC5Au4SbW+YcvdRiVG3W6nQio1tMW5Uje', region_name='us-east-1')

    with open('./configs/data_management_config.yaml') as f:
        run_time_config = yaml.safe_load(f)
    run_time_config['gpg_key'] = open('/home/<USER>/oeidp_sts.private.gpg').read()
    run_time_config['gpg_passphrase'] = 'experian0424'
    print(run_time_config)

    #local_config_file_dir = './config_files'
    #run_time_config = download_and_parse_config_file(boto3_client, args.config_file_path, local_config_file_dir)

    run_data_management(db_conn, boto3_client, run_time_config, run_on_airflow_flag=False, dry_run_flag=args.dry_run_flag)
