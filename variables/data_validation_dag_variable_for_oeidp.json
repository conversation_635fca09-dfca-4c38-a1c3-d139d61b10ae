[{"key": "data_validation_image_name", "value": "365007379098.dkr.ecr.us-east-1.amazonaws.com/airflow-datavalidation:0.6"}, {"key": "data_management_config", "value": "max_boto3_transfer_tries: 3\nchecksum_table_name: 'checksum'\ndata_table_name: 'data'\nmanifest_file_table_name: 'manifest_file'\nmanifest_info_table_name: 'manifest_info'\nlog_dir: 'logs'\ndata_root: 's3://signal-hub-landing-365007379098-us-east-1/us/'\narchive_root: 's3://signal-hub-365007379098-us-east-1/archive/us/'\narchive_bucket: 'signal-hub-365007379098-us-east-1'\nmalformed_file_folder: 's3://signal-hub-365007379098-us-east-1/oeidp_data_validation/malformed_files/'\nproject: 'oeidp'"}, {"key": "data_management_config_auto", "value": "max_boto3_transfer_tries: 3\nchecksum_table_name: 'checksum'\ndata_table_name: 'data'\nmanifest_file_table_name: 'manifest_file'\nmanifest_info_table_name: 'manifest_info'\nlog_dir: 'logs'\ndata_root: 's3://signal-hub-landing-365007379098-us-east-1/us/auto/'\narchive_root: 's3://signal-hub-365007379098-us-east-1/archive/us/auto/'\narchive_bucket: 'signal-hub-365007379098-us-east-1'\nmalformed_file_folder: 's3://signal-hub-365007379098-us-east-1/oeidp_data_validation/malformed_files/'\nproject: 'signal_hub'"}, {"key": "data_management_config_gvap", "value": "max_boto3_transfer_tries: 3\nchecksum_table_name: 'checksum'\ndata_table_name: 'data'\nmanifest_file_table_name: 'manifest_file'\nmanifest_info_table_name: 'manifest_info'\nlog_dir: 'logs'\ndata_root: 's3://signal-hub-landing-365007379098-us-east-1/us/cis/gvap/'\narchive_root: 's3://signal-hub-365007379098-us-east-1/archive/us/cis/gvap/'\narchive_bucket: 'signal-hub-365007379098-us-east-1'\nmalformed_file_folder: 's3://signal-hub-365007379098-us-east-1/oeidp_data_validation/malformed_files/'\nproject: 'signal_hub'"}, {"key": "data_management_config_ems", "value": "max_boto3_transfer_tries: 3\nchecksum_table_name: 'checksum'\ndata_table_name: 'data'\nmanifest_file_table_name: 'manifest_file'\nmanifest_info_table_name: 'manifest_info'\nlog_dir: 'logs'\ndata_root: 's3://signal-hub-landing-365007379098-us-east-1/us/ems/'\narchive_root: 's3://signal-hub-365007379098-us-east-1/archive/us/ems/'\narchive_bucket: 'signal-hub-365007379098-us-east-1'\nmalformed_file_folder: 's3://signal-hub-365007379098-us-east-1/oeidp_data_validation/malformed_files/'\nproject: 'signal_hub'"}, {"key": "validation_config", "value": "log_dir: 'logs'\ndefault_interval: 7\ntransfer_grace_period: 2\ndata_file_grace_period: 2\nalerting_period: 1\ntotal_line_cnt_change_thresh: 5"}, {"key": "cleanup_config", "value": "data_table_name: 'data'\nchecksum_table_name: 'checksum'\nmanifest_file_table_name: 'manifest_file'\ndirpath: 's3://signal-hub-landing-365007379098-us-east-1/us/'\nlog_dir: 'logs'\narchive_bucket: 'signal-hub-365007379098-us-east-1'\narchive_root: 's3://signal-hub-365007379098-us-east-1/archive/us/'\nmanagement_bucket: 'signal-hub-365007379098-us-east-1'\nmanagement_root: 's3://signal-hub-365007379098-us-east-1/us/experian'\nproject: 'oeidp'\ngrace_period: 0"}, {"key": "cleanup_config_auto", "value": "data_table_name: 'data'\nchecksum_table_name: 'checksum'\nmanifest_file_table_name: 'manifest_file'\ndirpath: 's3://signal-hub-landing-365007379098-us-east-1/us/auto/'\nlog_dir: 'logs'\narchive_bucket: 'signal-hub-365007379098-us-east-1'\narchive_root: 's3://signal-hub-365007379098-us-east-1/archive/us/auto/'\nmanagement_bucket: 'signal-hub-365007379098-us-east-1'\nmanagement_root: 's3://signal-hub-365007379098-us-east-1/us/experian'\nproject: 'signal_hub'\ngrace_period: 0"}, {"key": "cleanup_config_gvap", "value": "data_table_name: 'data'\nchecksum_table_name: 'checksum'\nmanifest_file_table_name: 'manifest_file'\ndirpath: 's3://signal-hub-landing-365007379098-us-east-1/us/cis/gvap/'\nlog_dir: 'logs'\narchive_bucket: 'signal-hub-365007379098-us-east-1'\narchive_root: 's3://signal-hub-365007379098-us-east-1/archive/us/cis/gvap/'\nmanagement_bucket: 'signal-hub-365007379098-us-east-1'\nmanagement_root: 's3://signal-hub-365007379098-us-east-1/us/experian'\nproject: 'signal_hub'\ngrace_period: 0"}, {"key": "cleanup_config_ems", "value": "data_table_name: 'data'\nchecksum_table_name: 'checksum'\nmanifest_file_table_name: 'manifest_file'\ndirpath: 's3://signal-hub-landing-365007379098-us-east-1/us/ems/'\nlog_dir: 'logs'\narchive_bucket: 'signal-hub-365007379098-us-east-1'\narchive_root: 's3://signal-hub-365007379098-us-east-1/archive/us/ems/'\nmanagement_bucket: 'signal-hub-365007379098-us-east-1'\nmanagement_root: 's3://signal-hub-365007379098-us-east-1/us/experian'\nproject: 'signal_hub'\ngrace_period: 0"}, {"key": "generate_semaphore_config", "value": "management_root: 's3://signal-hub-365007379098-us-east-1/us/experian'\nsemaphore_file_path_root: 's3://signal-hub-365007379098-us-east-1/us/experian/semaphore_file'\nincremental_datasources: ['fraudnet']"}, {"key": "dataset_list_for_data_source_validation", "value": "gvap:\n  - 'gvap_address'\n  - 'gvap_axn_lookup'\n  - 'gvap_cons_address'\n  - 'gvap_cons_common_name'\n  - 'gvap_cons_excluded_name'\n  - 'gvap_cons_name'\n  - 'gvap_cons_opt'\n  - 'gvap_cons_related_name'\n  - 'gvap_cons_telephone'\n  - 'gvap_db_id_rank'\n  - 'gvap_drv_lic_lookup'\n  - 'gvap_employment'\n  - 'gvap_facs_address'\n  - 'gvap_issued_ssn'\n  - 'gvap_phone_lookup'\n  - 'gvap_pxn_lookup'\n  - 'gvap_retired_ssn'\n  - 'gvap_ssn_lookup'\n  - 'gvap_pin_split_history'\n  - 'gvap_pin_merge_history'\nsource_truvue:\n  - 'truvue_address'\n  - 'truvue_cons_addr_assoc'\n  - 'truvue_cons_common_name'\n  - 'truvue_cons_excluded_name'\n  - 'truvue_consumer'\n  - 'truvue_consumer_opt'\n  - 'truvue_cons_related_name'\n  - 'truvue_consumer_telephone'\n  - 'truvue_db_id_cntl'\n  - 'truvue_pin_merge_history'\n  - 'truvue_source_master'\n  - 'truvue_bus_name'\n  - 'truvue_bus_phone'\n  - 'truvue_bus_id_merge_history'\n  - 'truvue_bxn_lookup'\n  - 'truvue_bus_phone_lkup'\n  - 'truvue_bus_addr_lkup'\n  - 'truvue_bus_addr_assoc'\n  - 'truvue_suprmstr'\nsource_emailinsight:\n  - 'ems_email'\n  - 'ems_eiscore'"}, {"key": "total_line_count_bootstrap_config", "value": "gvap:\n    gvap_employment:\n    - lc: 457617401\n      date: '2022-02-26'\n    gvap_pin_split_history:\n    - lc: 2128286\n      date: '2021-02-27'\n    gvap_pin_merge_history:\n    - lc: 263787458\n      date: '2021-02-27'\nsource_emailinsight:\n    ems_eiscore:\n    - lc: 178143392\n    ems_email:\n    - lc: 1348579847\nsource_linkage:\n    source_linkage:\n    - lc: 1048138779\nsource_cell:\n    source_cell:\n    - lc: 420222940\nsource_truvue:\n    truvue_consumer:\n    - lc: 1900209799\n      date: '2021-12-24'\ncem_clarity:\n    cem_clarity:\n    - lc: 94940523\n      date: '2022-03-28'\ncem_citygeocode:\n    cem_citygeocode:\n    - lc: 58093\n      date: '2022-03-11'\ncem_zipgeocode:\n    cem_zipgeocode:\n    - lc: 38983\n      date: '2022-03-11'\nrentbureau:\n    rentbureau:\n    - lc: 41873664\n      date: '2022-03-04'"}, {"key": "transfer_interval_config", "value": "datasource_level:\n    gvap: 31\n    source_truvue: 31\n    source_emailinsight: 31\ndataset_level:\n    cem_clarity: 1\n    cem_coa: 7\n    cem_phonepin: 7\n    cem_residential: 14\n    cem_advo: 31\n    cem_biz: 31\n    cem_cell: 31\n    cem_dmv: 31\n    cem_highrisk: 31\n    cem_citygeocode: 93\n    cem_zipgeocode: 93\n    cem_ofac:\n    cem_cellphone:\n    cem_quentinsager: 31\n    fraudnet_2co: 1\n    fraudnet_aa: 3\n    fraudnet_adcs: 1\n    fraudnet_aflac: 1\n    fraudnet_ally: 1\n    fraudnet_bbt: 1\n    fraudnet_bbva: 1\n    fraudnet_bcus: 1\n    fraudnet_bofa: 1\n    fraudnet_careerbuilder: 1\n    fraudnet_carscom: 1\n    fraudnet_charlesschwab: 1\n    fraudnet_comcast: 1\n    fraudnet_conns: 1\n    fraudnet_customersbank: 1\n    fraudnet_ecna: 3\n    fraudnet_hawaiian: 1\n    fraudnet_hsbc: 1\n    fraudnet_idme: 1\n    fraudnet_ncac: 3\n    fraudnet_neiman: 3\n    fraudnet_okta: 1\n    fraudnet_progrexion: 1\n    fraudnet_qvc: 1\n    fraudnet_santanderus: 1\n    fraudnet_sephora: 3\n    fraudnet_usbank: 1\n    fraudnet_wf: 1\n    fraudnet_zebit: 1\n    fraudnet_penfed: 1\n    fraudnet_pitney: 1\n    fraudnet_radial: 3\n    fraudnet_wirecash: 1\n    fraudnet_totalwine: 1\n    fraudnet_zag: 1\n    fraudnet_charter: 1\n    fraudnet_drs: 1\n    fraudnet_drs2: 1\n    fraudnet_worldfuel:\n    fraudnet_pnc:\n    nvdb: 31\n    rentbureau: 31\n    ems_eiscore: 31\n    ems_email: 31\n    source_cell: 91\n    source_linkage: 31"}, {"key": "fraudnet_alert_config", "value": "- 'fraudnet_aa'\n- 'fraudnet_ecna'\n- 'fraudnet_ncac'\n- 'fraudnet_radial'\n- 'fraudnet_neiman'\n- 'fraudnet_sephora'"}, {"key": "gpg_secret_key_name", "value": "dev/data-validation-encryption-secret"}, {"key": "gpg_passphrase", "value": "jc2KZ4D5"}, {"key": "gpg_private_key", "value": "-----BEG<PERSON> PGP PRIVATE KEY BLOCK-----\nVersion: GnuPG v2\n\nlQO9BFp46q8BCACio8BRN7DzeXuUE54dKEzfsLWE3Wp7vEnvibA1QYwiLJHTD1qD\nRnclr3ID07LKfX0GvfG5Ko+W0D/C3tvI/Bk/B/ERW9lRqkTUhKsJyGqVWkVUd0PC\nMnMNp4DC6sWUNfCZkuE9fRbervLLPKQa5BjZ7RCH9gXEyTaofjux+zdFK/CL7e5S\nIdoUBowJpDf5uRFP6VQftkaZIN954b2uTIqX6ekNnS+yMnAJdhgR32a+AzI1Gi70\n2OgmmOfQz0lJPx4l2NWsgRgWeepS5oBoDCFLfAsBpye3BN4D6I16Ube0iAZw+ElQ\nuv311da21thu9Zt07y4MLaW1+MYEnsca9hgPABEBAAH+AwMC5F4N9SrdiSjgX5Kv\nb5iXmlwPGgtcVK04FF6XZwe1KAbSyfDIPwoP/nWRPTSnoD/Yt8xCWfUtd+PE1R86\nkI3AsYtXUwIIvPXsyxCQq2zwj26pZELl3yppbnB2xZtHKZTdsiXymlaPuVLRm4wh\nIgZrapVYBd8racHmCs43CIYS4knsbt/nsbG4I2SQhBXyYKCzIkwzIy4VjgK41Lvl\n9bcIk15i7/mNVGPG5+KCAMy9PeXkNxQfk2JSjOI6l9Eg45W1xrIYlwkVai1bQKVa\nqgkt1XmPhYU0i4Swnx6A8dyZUkTCJZvFFQ1TfEcBMYZ1LHP0/0sxTGDGBFUxYHdL\neTJca6jBVwC9pAfAkUp5g4tno7HBz1bV+3O0NewXXXal1v4Lm0G8oRNf201oOIf1\nxsZDdTucNxzf1QHN+2RNWNjDh4wClHT5BXY8r519kAHQobSejsk4KcIiaTtGDmz9\nK6OUJzbt2TVTzCrtwEWEozwvUIXW6zYycWLAsp+pzrJRNjdW0Sve5aJ3eHxpZW9M\nSy5mWZQlD62ys57GtQA8eU3DZRRGFLk+0hALdEEtrL7TvBm+F0hQlKp378ULMqku\nXQFGhIyBNRqNlkOrWjsquCD3+7C+J8+hhFqls24oJPrndnEuUWsf3TcggYck4iSR\nX5LmZTi/io5e8+4mEJ3tVPvxZiZwRGRmPyY2WLZdjPdcY9iD6P9pppjgGdAAGDCD\nb18fUoq/k3VZPdMkgIzcWo4dcyfZCmmMQ89E7u0BqBbbjc3xAdDaLhr6NSgR8ICS\nQf/fQ6GuY/DKco6sqFBqXi2J0wtroAD1VQdJTfFQw2cAiaIdCZKSDpNdcM0s7mdX\nTjJKJPVbpzP6RAD5ST0rKOoYJnkfedc2sP4bkCwPIoCueEweJJL5eAusJKm4DhLG\ntAVvZWlkcIkBOQQTAQIAIwUCWnjqrwIbAwcLCQgHAwIBBhUIAgkKCwQWAgMBAh4B\nAheAAAoJEBKYm0z5StW8b7UIAIqYMrsL0hUa2XM9AtpJPlFdTm/+Tj8IrikeYcbK\ndz2DtRIeKkV6+dEQ7Z7lrp+RM1FyFtT6NkVyCxlmWx0ThrItxk2/O+ppzmHPi78k\nIlml/USXif+p2EQSNRgRs/rJniVAhR54onj1YAnqmbEndObylbzCY8Sn6O1vec4C\nguov8H3yMwe1JYOqi3pDy8eWNanBkwxZN901H5v+XQaiTiu4gA/Nb1puD31SqYMI\n/A5i+hCsA+BjGblzMO67Uwzn+EZgVmKsS3aEk8M5FBdFKyEb3VIjvYoFjOXDn+Hr\nu+OJRE3iYG6ocYzz86g0+tvJ9ymVQHRxw9WqS+YVFhxnDwqdA74EWnjqrwEIAMhM\n7yuTtY+DPU0FEJiX/bzfbezSmFZAjtKK3079Fwvs/iLy39n7MuVx0dABELHai6C7\nA8wLm12wlL/Xo6nHeBbIwJRhC4bv3w1QqLQlixNuiLgUfSc7ZMNF71bPZYix7SL3\n+3WRYr+GW414YdQKhXc1lHl6vF9Ze46oFzdkcSpGziqmJKjFucTXbuohjVlUtjSg\nbfIM3RFFWc/4YAiJMBPaMcgVBGd3n7Bd6n7RC0k6pBs5J7vYSkslUd2czqpTqARU\nbZOIHokDeKnojrjZ7oiNgaU49FhZI7Gama8W7g7O9DkY3l8e0ub1wmTLJ6NkzSQa\nvL02nNJitTDrXnoVXN0AEQEAAf4DAwLkXg31Kt2JKODOHQ+HCRBk3JDunXL7MK21\ntsTpa+RB7v3oasTSNiReEBtpgw/LlaNwbt52Pqina6zFb/Wccq/OnYJ6v2JvQrW/\nJ2ZWVht/HqfKUuNZwPhOaybRHgGD8ThSdxv0XdCTneTFKBIGnjJzFe4NZR1PPz09\nASMYgLxcjQO6SyC8Zf2Zz0EdyS8k8JWuUeS/EK4ZBIkenWIObx/scXjPw/VBDaH9\n7RXnwi4aaPBR4g2lgyh0OwOUL9idUhfF7aGy4yobF2NjZrUMgfoiINUOUs5yrryI\ncDqod6f7zS1AzYyI/JWHLSDaQlvIGvZSilrMv0o32GYSHDX9yItIf/Dut6BlK56O\nQDU/fJ4BAcgnVHL3b7NHU863hiNTXlBLY0h/7sQrORAQ/4wZu1RWpibAQ6LQoJAY\ncrk2NwdmXUPl2GRy7T+RGPoSykO6qgutZqCH4p9QBZ3fWMBBTVV5U9gx/D4mF+SL\nymNJAz6AujjJz8TwZgtopSK7SzFVHj798x+4Zd3buhb18QBk0CX294xu7rRiLXnz\nav2lbag8jHmIx4yK4+XjsjzMXEiGwxGL5xREVbcd4SO8t0X39oaYjIXGPEkog80p\nXQST/sxVDi/qAtyQziFW5u6iNLMlKDumyLx95vSKpiT5YYncYRF5Ca4QfDS/xB/9\nGNq0iuI2OPWz6zmowYyhvVrr5Tkg2RBtJknQkXViczFntngqK7y6BQMacUSca/NI\nARPSk/o6IzXHrQ2qFdMUCcAmiB4zvuF2CdU1VCPd5AFMNlnroCjwrk+ZOOdoqhZN\nhKUBgx/4k0Kjz0vmheiWEHFbp9NJad7LlPTvSYFNwGZ+HfremnB0cMhaVhM/BDme\nlvTcCbqoX+CB9Nbup+vnXe+pPoUya9vooj8R7jdiWjvrMQC/iQEfBBgBAgAJBQJa\neOqvAhsMAAoJEBKYm0z5StW8pMgIAIW6/yEOoHA76i2p2G7oNK/R5/wch7Yeb5lN\nnSTlzlZBHS6d0ohd91BwcyD4YdOwNUqriJriFMNu3vTxReOsLbQ4GKG8Un+ehQ+L\nTWEitL1Av++Gn50yoin1Dbvs5wBD7e5wP/We/kx84IPQXU7eYoOSz3a23ian/15S\nW7tVMlGr5OjRKUuR5iWAeQZZ6K9xxeDE6jXvXlp9K6xs/benzkTUSQFtqhvz5kHi\n7zCCzK9lpOF3ZappiCCxE5Fg8FNE4BxeI/u7o1GmkyCGPCOUsVgJLRML69Cg5uyF\ngDQGU9ei9Ca7Bs4tDhNBgfrlqIOfJ7jPSW9v/2Z1tu1hIyLeAOQ=\n=RjE0\n-----END PGP PRIVATE KEY BLOCK-----\n"}, {"key": "ms_teams_channel_url", "value": "https://experian.webhook.office.com/webhookb2/0fe411e9-e0d2-4a33-8646-0bd51855eeea@be67623c-1932-42a6-9d24-6c359fe5ea71/IncomingWebhook/777d5c17f9c44be580fdd155eb465318/178ebd7c-3a5f-4b4b-9ce5-ab63bfd1947b"}, {"key": "https_proxy_url", "value": "http://************:9090"}, {"key": "http_proxy_url", "value": "http://************:9090"}]