[{"conn_id": "postgres_database_connection", "conn_type": "postgres", "description": "Connection to the Postgres DB that stores metadata information of incoming data files", "host": "datavalidation-gutcejte8gzx9tb4.cx4vg35yknde.us-east-1.rds.amazonaws.com", "login": "postgres", "password": "dE4Kg1Ze339bUeuD1nOZELjpXoMUtc8zeYnYF30XCj7Kx25HMZnSRzX89nFxXiy1"}, {"conn_id": "emr_conn", "conn_type": "aws", "extra": "{\"Name\": \"oeidp-retro-custom-ami\", \"LogUri\": \"s3n://signal-hub-emr-logs-365007379098-us-east-1/\", \"ReleaseLabel\": \"emr-5.36.0\", \"VisibleToAllUsers\": true, \"Applications\": [{\"Name\": \"Hadoop\"}, {\"Name\": \"Spark\"}], \"Tags\": [{\"Key\": \"Name\", \"Value\": \"emr-instance\"}], \"JobFlowRole\": \"arn:aws:iam::365007379098:instance-profile/BURoleForEMREC2DefaultRole\", \"ServiceRole\": \"arn:aws:iam::365007379098:role/eec-aws-standard-emr-defaultrole\", \"SecurityConfiguration\": \"SignalHubSecurityConfiguration\", \"Configurations\": [{\"Classification\": \"spark-hive-site\", \"Properties\": {\"aws.glue.catalog.separator\": \"/\", \"hive.metastore.client.factory.class\": \"com.amazonaws.glue.catalog.metastore.AWSGlueDataCatalogHiveClientFactory\"}}, {\"Classification\": \"hive-site\", \"Properties\": {\"aws.glue.catalog.separator\": \"/\"}}], \"ScaleDownBehavior\": \"TERMINATE_AT_TASK_COMPLETION\", \"EbsRootVolumeSize\": 30, \"AutoTerminationPolicy\": {\"IdleTimeout\": 600}, \"BootstrapActions\": [{\"Name\": \"OEIDP-Bootstrap\", \"ScriptBootstrapAction\": {\"Path\": \"s3://signal-hub-365007379098-us-east-1/applications/emr/oeidp/bootstraps/mgmt-bootstrap.sh\"}}], \"Instances\": {\"KeepJobFlowAliveWhenNoSteps\": true, \"TerminationProtected\": false, \"Ec2KeyName\": \"ec2\", \"Ec2SubnetId\": \"subnet-04aab33b94791472f\", \"InstanceGroups\": [{\"InstanceCount\": 16, \"InstanceType\": \"i3.4xlarge\", \"Market\": \"ON_DEMAND\", \"Name\": \"Core\", \"InstanceRole\": \"CORE\"}, {\"InstanceRole\": \"MASTER\", \"InstanceCount\": 1, \"InstanceType\": \"m5.2xlarge\", \"Market\": \"ON_DEMAND\", \"Name\": \"Master\", \"EbsConfiguration\": {\"EbsBlockDeviceConfigs\": [{\"VolumeSpecification\": {\"SizeInGB\": 64, \"VolumeType\": \"gp2\"}, \"VolumesPerInstance\": 1}], \"EbsOptimized\": true}}], \"AdditionalMasterSecurityGroups\": [], \"AdditionalSlaveSecurityGroups\": []}}"}, {"conn_id": "aws_conn", "conn_type": "aws", "extra": "{\"role_arn\": \"arn:aws:iam::365007379098:role/BURoleForAirflowDatalab\", \"region_name\": \"us-east-1\", \"assume_role_kwargs\": {}}"}, {"conn_id": "aws_gcp_conn", "conn_type": "aws", "extra": "{\"role_arn\": \"arn:aws:iam::365007379098:role/BURoleForGCPDeFiBigQuery\", \"region_name\": \"us-east-1\", \"assume_role_kwargs\": {}}"}]