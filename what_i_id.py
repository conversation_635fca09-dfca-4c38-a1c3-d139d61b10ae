# processing_date is yesterday of execution date, generate in test step
# yesterday = (datetime(execution_date.year, execution_date.month, execution_date.day) - timedelta(days=1)).strftime(%Y%m%d)

"""
SELECT 
    data_source, data_set, snapshot,
    snapshot_dt, manifest_location,
    integrity_validated, line_cnt_validated, checksum_validated
FROM
    validation
WHERE
    DATE(receiving_time) = 20250825 AND data_source != fraudnet;
"""

"""
SELECT 
    data_source, data_set, snapshot,
    snapshot_dt, manifest_location,
    integrity_validated, line_cnt_validated, checksum_validated
FROM
    validation
WHERE
    DATE(receiving_time) = 20250821 AND data_source != fraudnet;
"""

"""
data_source |  data_set   | snapshot | snapshot_dt |                                          manifest_location                                           | integrity_validated | line_cnt_v
alidated | checksum_validated
-------------+-------------+----------+-------------+------------------------------------------------------------------------------------------------------+---------------------+-----------
---------+--------------------
 cem         | cem_ofac    | 20250821 | 2025-08-21  | s3://signal-hub-landing-365007379098-us-east-1/us/cis/cem/cem_ofac.20250821.19462321.manifest.txt    |                   1 |
       1 |                  1
 cem         | cem_clarity | 20250821 | 2025-08-21  | s3://signal-hub-landing-365007379098-us-east-1/us/cis/cem/cem_clarity.20250821.19462321.manifest.txt |                   1 |
       1 |
"""

"""
SELECT 
    data_source, data_set, snapshot,
    snapshot_dt, manifest_location,
    integrity_validated, line_cnt_validated, checksum_validated
FROM
    validation
WHERE
    DATE(receiving_time) = 20250826 AND data_source != fraudnet;
"""

"""
 data_source | data_set | snapshot | snapshot_dt |                                         manifest_location                                         | integrity_validated | line_cnt_validat
ed | checksum_validated
-------------+----------+----------+-------------+---------------------------------------------------------------------------------------------------+---------------------+-----------------
---+--------------------
 cem         | cem_cell | 20250826 | 2025-08-26  | s3://signal-hub-landing-365007379098-us-east-1/us/cis/cem/cem_cell.20250826.19535563.manifest.txt |                   1 |
 1 |                  1
(1 row)

"""


# =========================

"""
SELECT 
    null, snapshot_dt, SUBSTRING(snapshot, 1, 8) AS snapshot FROM data
WHERE 
    data_source = fraudnet AND
    DATE(last_modified_time) = 20250821
    GROUP BY snapshot_dt, SUBSTRING(snapshot, 1, 8);
"""
"""
 ?column? | snapshot_dt | snapshot
----------+-------------+----------
          | 2025-08-20  | 20250820
          | 2025-08-21  | 20250820
          | 2025-08-21  | 20250821
(3 rows)
"""

"""
SELECT 
    null, snapshot_dt, SUBSTRING(snapshot, 1, 8) AS snapshot FROM data
WHERE 
    data_source = fraudnet AND
    DATE(last_modified_time) = 20250826
    GROUP BY snapshot_dt, SUBSTRING(snapshot, 1, 8);
"""

"""
 ?column? | snapshot_dt | snapshot
----------+-------------+----------
          | 2025-08-25  | 20250825
          | 2025-08-26  | 20250825
          | 2025-08-26  | 20250826

"""


# =======================

# get_list_of_bz2_files

"""
SELECT archived_destination, bz2_file_archived_destination, snapshot_dt FROM data
                WHERE checksum_validated = 1 AND data_source = fraudnet AND SUBSTRING(snapshot, 1, 8) = 20250821;
"""

"""
SELECT archived_destination, bz2_file_archived_destination, snapshot_dt FROM data
                WHERE checksum_validated = 1 AND data_source = fraudnet AND SUBSTRING(snapshot, 1, 8) = 20250826;

"""


"""
SELECT
    data_key,
    location,
    line_cnt_validated,
    checksum_validated,
    line_cnt_against,
    checksum_against,
    line_cnt,
    checksum_value,
    last_modified_time,
    data_source,
    invalid_name
FROM
    data
WHERE
    alert_time IS NULL
    AND (
        line_cnt_validated = 0
        OR line_cnt_validated IS NULL
        OR checksum_validated = 0
        OR checksum_validated IS NULL
    )
ORDER BY
    archived_time ASC;
"""

"s3://signal-hub-landing-365007379098-us-east-1/us/gfid/fraudnet/fraudnet_ecna.20250901153516.20250901160155.4639350458_4639400839_001.bz2"


check_corresponding_manifest = WHEN validation.manifest_location IS NULL THEN NULL
match_manifest_tmp_with_validation = manifest_tmp.data_source = validation.data_source AND manifest_tmp.data_set = validation.data_set AND manifest_tmp.snapshot = validation.snapshot

"""
SELECT 
    manifest_key, 
    MAX(xfer_time) AS manifest_xfer_time, 
    MAX(location) AS location, 
    MAX(data_source) AS data_source, 
    MAX(data_set) AS data_set, 
    MAX(snapshot) AS snapshot, 
    MAX(snapshot_dt) AS snapshot_dt 
FROM 
    manifest_file 
GROUP BY 
    manifest_key
"""

"""

UPDATE data
SET line_cnt_validated = CASE
    WHEN (
        SELECT manifest_tmp.line_cnt
        FROM manifest_tmp
        WHERE manifest_tmp.manifest_location = data.line_cnt_against
          AND manifest_tmp.data_key = data.data_key
    ) IS NULL OR data.line_cnt IS NULL THEN NULL
    WHEN (
        SELECT manifest_tmp.line_cnt
        FROM manifest_tmp
        WHERE manifest_tmp.manifest_location = data.line_cnt_against
          AND manifest_tmp.data_key = data.data_key
    ) = data.line_cnt THEN 1
    ELSE 0


"""