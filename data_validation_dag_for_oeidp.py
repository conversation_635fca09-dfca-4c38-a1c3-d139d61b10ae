import io
import os
import json
import yaml
import shutil
import subprocess
import boto3
import pandas as pd
from datetime import date, datetime, timedelta
from airflow import DAG
from airflow.operators.python import get_current_context
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import Python<PERSON>perator
from airflow.operators.bash_operator import <PERSON><PERSON><PERSON>perator
from airflow.sensors.base_sensor_operator import BaseSensorOperator
from airflow.providers.amazon.aws.hooks.base_aws import AwsBaseHook
from airflow.providers.amazon.aws.hooks.secrets_manager import SecretsManagerHook
from airflow.hooks.base import BaseHook
from airflow.hooks.S3_hook import S3Hook
from airflow.models import Variable
from airflow.decorators import dag, task
from kubernetes.client import models as k8s

from signalhub.datavalidation.utils.utilities import create_connection, db_operation, get_bucket_and_key_from_filename,  get_head_object, get_head_object_using_airflow, calculate_checksum_using_aws_cli, calculate_line_count_using_aws_cli, decrypt_gpg_file, decrypt_gpg_file_using_aws_cli, create_local_directory, copy_file_on_s3, copy_file_on_s3_using_airflow, get_date_from_cc1_file, calculate_checksum_and_line_count, calculate_checksum_and_line_count_using_aws_cli, download_file_from_s3_to_local_using_aws_cli, upload_file_from_local_to_s3_using_aws_cli, convert_file_to_bz2_using_aws_cli
from signalhub.datavalidation.utils.data_management import run_data_management
from signalhub.datavalidation.utils.validation import run_validation
from signalhub.datavalidation.utils.cleanup import run_cleanup, get_list_of_valid_snapshots, get_list_of_bz2_files, copy_snapshot_from_archive


team_name = 'datalab'
mem_dir = "/tmp"


def connect_to_postgres_db(**context):
    connection = BaseHook.get_connection(f'{team_name}_postgres_database_connection')
    db_conn = create_connection(connection.host, connection.login, connection.password)
    return db_conn


def get_aws_credentials():
    creds = AwsBaseHook(f"{team_name}_aws_conn", client_type="s3").get_credentials()
    return dict(
        AWS_ACCESS_KEY_ID=creds.access_key,
        AWS_SECRET_ACCESS_KEY=creds.secret_key,
        AWS_SESSION_TOKEN=creds.token,
    )


def get_environment():
    conn = BaseHook.get_connection(f'{team_name}_aws_conn')
    print("extra: " + conn.get_extra())
    conn_extra = json.loads(conn.get_extra())
    print("extra loaded: " + str(conn_extra))
    env = os.environ.copy()
    env.update(dict(
        AWS_IAM_ROLE_ASSUME=conn_extra.get("role_arn"),
        AWS_REGION=conn_extra.get("region_name", "us-east-1")
    ))
    return env


def gpg_decrypt(decrypt_script, env):
    cmd = ["bash", decrypt_script]
    process_read_raw = subprocess.Popen(cmd, env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    std_out, std_err = process_read_raw.communicate()
    print('STD_ERR:', std_err)
    print('STD_OUT:', std_out)


@dag(
    schedule_interval='0 15 * * *',
    start_date=datetime(2022, 1, 1, 0, 0),
    #end_date=datetime(2022, 2, 2, 0, 0),
    catchup=False,
    tags=['example'],
)
def data_validation_dag_for_oeidp():

    #executor_config_sidecar = {
    #    "pod_override": k8s.V1Pod(
    #        spec=k8s.V1PodSpec(
    #            containers=[
    #                k8s.V1Container(
    #                    image=Variable.get(f'{team_name}_data_validation_image_name'),
    #                    name="base",
    #                    command=[""],
    #                    image_pull_policy="Always",
    #                ),
    #            ]
    #        )
    #    ),
    #}

    executor_config_sidecar = {
        "pod_override": k8s.V1Pod(
            spec=k8s.V1PodSpec(
                containers=[
                    k8s.V1Container(
                        image=Variable.get(f'{team_name}_data_validation_image_name'),
                        name="base",
                        image_pull_policy="Always"
                    ),
                ]
            )
        ),
    }

    @task(executor_config=executor_config_sidecar)
    def test():
        print('This is a test')

        ## access variables
        image = Variable.get(f'{team_name}_data_validation_image_name')
        print('image:', image)

        db_conn = connect_to_postgres_db()
        res = db_operation(db_conn, 'SELECT count(*) from validation', [])
        print(res)

        #print('create boto3 client...')
        #boto3_client = S3Hook(f'{team_name}_aws_conn').get_conn()
        #print('boto3 client is ready')

        context = get_current_context()
        execution_date = context.get('execution_date', '')
        print('EXECUTION_DATE:', execution_date)
        yesterday = (datetime(execution_date.year, execution_date.month, execution_date.day) - timedelta(days=1)).strftime('%Y%m%d')
        print('YESTERDAY:', yesterday)

        env = get_environment()

        ## test copying large data files
        file_name = 's3://signal-hub-landing-365007379098-us-east-1/us/ems/source_linkage/ems_linkage.X279.20220817092154.rng07.gz'
        destination = 's3://signal-hub-365007379098-us-east-1/archive/us/ems/source_linkage/ems_linkage.X279.20220817092154.rng07.gz'
        #file_name = 's3://signal-hub-365007379098-us-east-1/oeidp_data_validation/landing_zone_temp/large_files/nvdb/nvdb.20221026092350.20221026092350.00.gz'
        #destination = 's3://signal-hub-365007379098-us-east-1/oeidp_data_validation/archive_zone_temp/us/auto/nvdb/nvdb.20221026092350.20221026092350.00.gz'
        source_bucket, source_key = get_bucket_and_key_from_filename(file_name)
        dest_bucket, dest_key = get_bucket_and_key_from_filename(destination)
        #copy_file_on_s3_using_airflow_new(source_bucket, source_key, dest_bucket, dest_key)

        ## test compression of large data files
        print('CREDENTIAL:', env)
        #file_name = 's3://signal-hub-landing-365007379098-us-east-1/us/auto/nvdb/nvdb.20221227090254.20221227090254.00.gz'
        #file_name = 's3://signal-hub-365007379098-us-east-1/oeidp_data_validation/landing_zone_temp/large_files/nvdb/nvdb.20221026092350.20221026092350.00.gz'
        # file_name = 's3://signal-hub-365007379098-us-east-1/oeidp_data_validation/landing_zone_temp/us/ems/source_linkage/ems_linkage.X282.20221105030838.rng01.gz'
        #file_name = 's3://signal-hub-landing-365007379098-us-east-1/us/cis/cem/cem_cell.130_20221008090328.20221008095206.1.gz'
        #file_name = 's3://signal-hub-365007379098-us-east-1/oeidp_data_validation/landing_zone_temp/us/cis/cem/cem_advo.331_20221106090029.20221106095132.1.gz'
        # file_name = 's3://signal-hub-landing-365007379098-us-east-1/us/cis/cem/cem_clarity.3683_20221216090141.20221216093313.1.gz'

        # data_keys = ['cem_clarity.3684_20221217090041.20221217092815.1.gz', 'cem_clarity.3685_20221218090137.20221218093211.1.gz', 'cem_clarity.3686_20221219090047.20221219092729.1.gz']
        # file_names = [f's3://signal-hub-landing-365007379098-us-east-1/us/cis/cem/{k}' for k in data_keys]

        data_keys = ['ems_email.X294.20231030031105.rng00.gz']
        file_names = [f's3://signal-hub-365007379098-us-east-1/archive/us/ems/source_emailinsight/{k}' for k in data_keys]

        #local_file = download_file_from_s3_to_local_using_aws_cli(file_name, creds=env, mem_dir=mem_dir)
        #print(f'LOCAL FILE: {local_file}')
        # file_name = 's3://signal-hub-365007379098-us-east-1/archive/us/ems/source_emailinsight/ems_email.X294.20231030031105.rng00.gz'
        # print(f'Start to convert file {file_name}')
        # converted_file, converted_file_checksum = convert_file_to_bz2_using_aws_cli(file_name, convert_in_local=False, creds=env, mem_dir=mem_dir, calculate_converted_file_checksum=True)
        # print('File is converted!')
        # print(f'Converted file checksum: {converted_file_checksum}')
        #upload_file_from_local_to_s3_using_aws_cli(converted_file, file_name.replace('.gz', '.bz2.1'), creds=env, mem_dir=mem_dir)
        #print('File is uploaded!')

        # for file_name in file_names:
        #     converted_file, converted_file_checksum = convert_file_to_bz2_using_aws_cli(file_name, creds=env, mem_dir=mem_dir, calculate_converted_file_checksum=True)
        #     print('converted_file:', converted_file)
        #     print('converted_file_checksum:', converted_file_checksum)

        ## test line count
        #file_name = 's3://signal-hub-365007379098-us-east-1/oeidp_data_validation/landing_zone_temp/large_files/nvdb/nvdb.20221026092350.20221026092350.00.bz2'
        #file_name = 's3://signal-hub-365007379098-us-east-1/oeidp_data_validation/landing_zone_temp/us/cis/cem/cem_advo.331_20221106090029.20221106095132.1.gz'
        file_name = 's3://signal-hub-landing-365007379098-us-east-1/us/auto/nvdb/nvdb.20220726091428.20220726091428.00.bz2'
        #line_count = calculate_line_count_using_aws_cli(file_name, in_local=True, creds=get_environment(), mem_dir=mem_dir)
        #local_file = download_file_from_s3_to_local_using_aws_cli(file_name, creds=env, mem_dir=mem_dir)
        #cmd = f'bzcat {local_file} | wc -l'
        #process_line_count = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
        #std_out, std_err = process_line_count.communicate()
        #line_count = std_out.decode('utf-8').strip()
        #print('LINE COUNT:', line_count)

        ## test checksum
        file_name = 's3://signal-hub-365007379098-us-east-1/oeidp_data_validation/landing_zone_temp/large_files/nvdb/nvdb.20221026092350.20221026092350.00.gz'
        #checksum = calculate_checksum_using_aws_cli(file_name, creds=get_environment(), mem_dir=mem_dir)
        #print('CHECKSUM:', checksum)

        #bucket = 'signal-hub-landing-365007379098-us-east-1'
        #key = 'us/ecs/clickstream/ecs_clickstream.202207010000.202210010000.manifest'
        #file_read = boto3_client.get_object(Bucket=bucket, Key=key)["Body"].read()
        #print(file_read)
        #file_lines = file_read.decode('utf-8').splitlines()
        #print(file_lines)

        #bucket = 'signal-hub-landing-365007379098-us-east-1'
        #key = 'us/ecs/membership/ecs_membership.202210010000.202210010000.manifest'
        #file_read = boto3_client.get_object(Bucket=bucket, Key=key)["Body"].read()
        #print(file_read)
        #file_lines = file_read.decode('utf-16', 'ignore').splitlines()
        #print(file_lines)

        ## test cleanup
        # valid_snapshots = get_list_of_valid_snapshots(db_conn)
        # print(f'# valid snapshots: {len(valid_snapshots)}')
        # print(valid_snapshots[:3])

        valid_files = []
        # for manifest_location, snapshot_dt, snapshot in valid_snapshots:
        #     bz2_files = get_list_of_bz2_files(db_conn, manifest_location, snapshot_dt)
        #     valid_files = valid_files + bz2_files
        # print(f'# valid files: {len(valid_files)}')
        # print(valid_files[:3])

        db_conn.close()

        return yesterday


    @task(executor_config=executor_config_sidecar)
    def data_management_step():

        print('====== start data_management ======')

        context = get_current_context()
        db_conn = connect_to_postgres_db()
        boto3_client = S3Hook(f'{team_name}_aws_conn').get_conn()
        env = get_environment()

        gpg_key = Variable.get(f'{team_name}_gpg_private_key')
        gpg_passphrase = Variable.get(f'{team_name}_gpg_passphrase')

        run_time_config = yaml.safe_load(Variable.get(f'{team_name}_data_management_config'))
        run_time_config['gpg_key'] = gpg_key 
        run_time_config['gpg_passphrase'] = gpg_passphrase
        run_time_config['aws_conn_id'] = f'{team_name}_aws_conn'
        run_time_config['credentials'] = env
        run_time_config['mem_dir'] = mem_dir
        run_time_config['ms_teams_channel_url'] = Variable.get(f'{team_name}_ms_teams_channel_url')
        run_time_config['https_proxy_url'] = Variable.get(f'{team_name}_https_proxy_url')
        run_time_config['http_proxy_url'] = Variable.get(f'{team_name}_http_proxy_url')
        run_time_config['start_date'] = '19000101'
        execution_date = context.get('execution_date', '')
        print('EXECUTION_DATE:', execution_date)
        run_time_config['end_date'] = (datetime(execution_date.year, execution_date.month, execution_date.day) - timedelta(days=0)).strftime('%Y%m%d')
        # run_time_config['end_date'] = context['ti'].xcom_pull(key='return_value', task_ids='test')
        # run_time_config['end_date'] = '20301231'
        print(run_time_config)
        run_data_management(db_conn, boto3_client, run_time_config)
        db_conn.close()
    

    @task(executor_config=executor_config_sidecar)
    def validation_step():
        db_conn = connect_to_postgres_db()
        config_package = {}
        config_package['validation_config'] = yaml.safe_load(Variable.get(f'{team_name}_validation_config'))
        config_package['interval_dict'] = yaml.safe_load(Variable.get(f'{team_name}_transfer_interval_config'))
        config_package['dataset_dict'] = yaml.safe_load(Variable.get(f'{team_name}_dataset_list_for_data_source_validation'))
        config_package['total_line_count_bootstrap_dict'] = yaml.safe_load(Variable.get(f'{team_name}_total_line_count_bootstrap_config'))
        config_package['fraudnet_alert_list'] = yaml.safe_load(Variable.get(f'{team_name}_fraudnet_alert_config'))
        config_package['ms_teams_channel_url'] = Variable.get(f'{team_name}_ms_teams_channel_url')
        config_package['https_proxy_url'] = Variable.get(f'{team_name}_https_proxy_url')
        config_package['http_proxy_url'] = Variable.get(f'{team_name}_http_proxy_url')
        run_validation(db_conn, config_package)
        db_conn.close()
    

    @task(executor_config=executor_config_sidecar)
    def cleanup_step():
        context = get_current_context()
        db_conn = connect_to_postgres_db()
        s3_hook = S3Hook(f'{team_name}_aws_conn')
        args = yaml.safe_load(Variable.get(f'{team_name}_cleanup_config'))
        args['aws_conn_id'] = f'{team_name}_aws_conn'
        args['ms_teams_channel_url'] = Variable.get(f'{team_name}_ms_teams_channel_url')
        args['https_proxy_url'] = Variable.get(f'{team_name}_https_proxy_url')
        args['http_proxy_url'] = Variable.get(f'{team_name}_http_proxy_url')
        args['start_date'] = '19000101'
        execution_date = context.get('execution_date', '')
        print('EXECUTION_DATE:', execution_date)
        args['end_date'] = (datetime(execution_date.year, execution_date.month, execution_date.day) - timedelta(days=0)).strftime('%Y%m%d')
        # args['end_date'] = context['ti'].xcom_pull(key='return_value', task_ids='test')
        # args['end_date'] = '20301231'
        args['project'] = 'oeidp'
        print(args)
        # for processing_date in pd.date_range(start='2023-06-22', end='2023-06-26', freq='D').strftime('%Y%m%d').tolist():
        #     print(f'====== Copying files for {processing_date} ======')
        #     processing_date = datetime.strptime(processing_date, "%Y%m%d").date()
        #     copy_snapshot_from_archive(db_conn, args, processing_date)
        processing_date = datetime.strptime(context['ti'].xcom_pull(key='return_value', task_ids='test'), "%Y%m%d").date()
        copy_snapshot_from_archive(db_conn, args, processing_date)
        run_cleanup(db_conn, s3_hook.get_conn(), args)
        db_conn.close()

    #test()
    #test() >> data_management_step()
    #test() >> validation_step()
    #test() >> cleanup_step()
    #test() >> data_management_step() >> validation_step()
    #test() >> validation_step() >> cleanup_step()
    test() >> data_management_step() >> validation_step() >> cleanup_step()

data_validation = data_validation_dag_for_oeidp()
